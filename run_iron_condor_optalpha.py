#!/usr/bin/env python3
"""
Iron Condor Strategy with OptAlpha Integration

This script demonstrates how to run an Iron Condor strategy using the full OptAlpha framework
with our comprehensive SPX options dataset.
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
import logging
import asyncio

# Add quantlab to path
sys.path.append('quantlab')

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_spx_options_data():
    """Load SPX options data for OptAlpha backtesting."""
    
    data_dir = Path("data")
    
    # Try to load the master file first (faster)
    master_file = data_dir / "spx_options_24months_optimized.parquet"
    
    if master_file.exists():
        try:
            df = pd.read_parquet(master_file)
            logger.info(f"✅ Loaded master file: {len(df):,} records")
            return df
        except Exception as e:
            logger.warning(f"⚠️  Failed to load master file: {e}")
    
    # Fallback to individual parquet files
    parquet_files = list(data_dir.glob("spx_options_*.parquet"))
    
    if not parquet_files:
        logger.error("❌ No SPX options parquet files found!")
        return None
    
    # Load recent files for analysis (last 30 days for faster processing)
    dfs = []
    for pf in sorted(parquet_files)[-30:]:
        try:
            df = pd.read_parquet(pf)
            dfs.append(df)
            logger.info(f"  ✅ Loaded {pf.name}: {len(df):,} records")
        except Exception as e:
            logger.warning(f"  ⚠️  Failed to load {pf.name}: {e}")
    
    if dfs:
        combined_df = pd.concat(dfs, ignore_index=True)
        logger.info(f"✅ Combined dataset: {len(combined_df):,} records")
        return combined_df
    
    return None

def prepare_optalpha_data(spx_data):
    """Prepare SPX options data for OptAlpha format."""
    
    logger.info("🔄 Preparing data for OptAlpha...")
    
    # Convert date column to datetime
    spx_data['quotedate'] = pd.to_datetime(spx_data['date'])
    
    # Parse contract information
    spx_data['contract_type'] = spx_data['contract'].str.extract(r'([CP])')[0]
    spx_data['strike'] = spx_data['contract'].str.extract(r'([CP])(\d{8})')[1].astype(float) / 1000
    spx_data['expiry'] = pd.to_datetime(spx_data['contract'].str.extract(r'(\d{6})')[0], format='%y%m%d')
    
    # Map contract types
    spx_data['type'] = spx_data['contract_type'].map({'C': 'call', 'P': 'put'})
    
    # Add required OptAlpha columns
    spx_data['underlying_last'] = spx_data['spx_price']
    spx_data['dte'] = (spx_data['expiry'] - spx_data['quotedate']).dt.days
    
    # Rename columns to match OptAlpha format
    column_mapping = {
        'last': 'last',
        'bid': 'bid', 
        'ask': 'ask',
        'volume': 'volume',
        'open_interest': 'openinterest'
    }
    
    for old_col, new_col in column_mapping.items():
        if old_col in spx_data.columns:
            spx_data[new_col] = spx_data[old_col]
    
    # Filter for reasonable DTE range
    spx_data = spx_data[(spx_data['dte'] >= 7) & (spx_data['dte'] <= 60)]
    
    logger.info(f"✅ Data prepared: {len(spx_data):,} records")
    return spx_data

def create_iron_condor_strategy():
    """Create an Iron Condor options trading strategy for OptAlpha."""
    
    try:
        from quantlab.optalpha import OPTION_SELECTOR
        
        # Iron Condor Strategy
        strategy = {
            "underlying": 0,  # No direct underlying exposure
            "legs": {
                # Short Put (sell OTM put)
                "short_put": {
                    "type": "put",
                    "contracts": -1,  # Sell 1 put
                    "selector": [
                        (OPTION_SELECTOR.STRIKE.ATM_REFERENCE_JUMP, -250),  # 5% OTM put
                        (OPTION_SELECTOR.EXPIRY.NEAREST, 30)  # ~30 days to expiration
                    ]
                },
                # Long Put (buy further OTM put for protection)
                "long_put": {
                    "type": "put",
                    "contracts": 1,  # Buy 1 put
                    "selector": [
                        (OPTION_SELECTOR.STRIKE.ATM_REFERENCE_JUMP, -300),  # Further OTM put
                        (OPTION_SELECTOR.EXPIRY.NEAREST, 30)  # ~30 days to expiration
                    ]
                },
                # Short Call (sell OTM call)
                "short_call": {
                    "type": "call",
                    "contracts": -1,  # Sell 1 call
                    "selector": [
                        (OPTION_SELECTOR.STRIKE.ATM_REFERENCE_JUMP, 250),  # 5% OTM call
                        (OPTION_SELECTOR.EXPIRY.NEAREST, 30)  # ~30 days to expiration
                    ]
                },
                # Long Call (buy further OTM call for protection)
                "long_call": {
                    "type": "call",
                    "contracts": 1,  # Buy 1 call
                    "selector": [
                        (OPTION_SELECTOR.STRIKE.ATM_REFERENCE_JUMP, 300),  # Further OTM call
                        (OPTION_SELECTOR.EXPIRY.NEAREST, 30)  # ~30 days to expiration
                    ]
                }
            }
        }
        
        return strategy
        
    except ImportError as e:
        logger.error(f"❌ Failed to import OptAlpha components: {e}")
        return None

async def run_iron_condor_optalpha():
    """Run Iron Condor strategy with OptAlpha framework."""
    
    print("🧬 Iron Condor Strategy with OptAlpha")
    print("=" * 45)
    
    # Load SPX options data
    spx_data = load_spx_options_data()
    
    if spx_data is None:
        print("❌ No SPX options data available")
        return False
    
    # Prepare data for OptAlpha
    optalpha_data = prepare_optalpha_data(spx_data)
    
    # Define trading period (use recent data for faster processing)
    end_date = pd.to_datetime(optalpha_data['quotedate'].max())
    start_date = end_date - timedelta(days=30)  # 1 month of data
    
    trade_range = (start_date, end_date)
    
    logger.info(f"📅 Trading period: {start_date.date()} to {end_date.date()}")
    
    # Create Iron Condor strategy
    strategy = create_iron_condor_strategy()
    
    if strategy is None:
        print("❌ Failed to create Iron Condor strategy")
        return False
    
    # Create rebalancing triggers
    try:
        from quantlab.optalpha import REBALANCER
        
        rebalance_triggers = [
            (REBALANCER.ON_DAYS_HELD, 7)  # Rebalance weekly
        ]
        
    except ImportError:
        logger.warning("⚠️  Using simplified rebalancing")
        rebalance_triggers = []
    
    # Initialize OptAlpha
    try:
        from quantlab.gene import GeneticOptAlpha, Gene

        # Create ticker dataframes dictionary
        ticker_dfs = {"SPX": optalpha_data}

        # Create a simple genome for the strategy
        genome = Gene.str_to_gene("const_1")  # Simple constant signal

        # Initialize GeneticOptAlpha with Iron Condor strategy
        iron_condor_alpha = GeneticOptAlpha(
            genome=genome,
            instruments=["SPX"],
            trade_range=trade_range,
            dfs=ticker_dfs,
            strategy=strategy,
            rebalance_triggers=rebalance_triggers,
            portfolio_vol=0.15
        )

        logger.info("✅ GeneticOptAlpha initialized with Iron Condor strategy")

    except Exception as e:
        logger.error(f"❌ Failed to initialize GeneticOptAlpha: {e}")
        print(f"❌ GeneticOptAlpha initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Run simulation
    logger.info("🏃 Running Iron Condor simulation...")
    
    try:
        portfolio_df = await iron_condor_alpha.run_simulation()
        
        # Get performance statistics
        perf_stats = iron_condor_alpha.get_perf_stats(plot=True)
        
        print(f"\n🎉 IRON CONDOR SIMULATION COMPLETED!")
        print("=" * 45)
        print(f"📊 Portfolio Performance:")
        print(f"   Total Return: {perf_stats.get('total_return', 'N/A')}")
        print(f"   Sharpe Ratio: {perf_stats.get('sharpe_ratio', 'N/A')}")
        print(f"   Max Drawdown: {perf_stats.get('max_drawdown', 'N/A')}")
        print(f"   Win Rate: {perf_stats.get('win_rate', 'N/A')}")
        
        # Save results
        results_file = f"iron_condor_optalpha_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        portfolio_df.to_csv(results_file)
        logger.info(f"💾 Results saved to: {results_file}")
        
        print(f"\n📋 Iron Condor Strategy Details:")
        print(f"   • Short Put: 5% OTM")
        print(f"   • Long Put: Further OTM protection")
        print(f"   • Short Call: 5% OTM")
        print(f"   • Long Call: Further OTM protection")
        print(f"   • Target DTE: ~30 days")
        print(f"   • Rebalance: Weekly")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Simulation failed: {e}")
        import traceback
        traceback.print_exc()
        print(f"❌ Iron Condor simulation failed: {e}")
        return False

def main():
    """Main function."""
    
    print("🎯 Iron Condor Strategy with OptAlpha Framework")
    print("=" * 55)
    
    # Run the Iron Condor example
    success = asyncio.run(run_iron_condor_optalpha())
    
    if success:
        print("\n✅ Iron Condor OptAlpha example completed successfully!")
        print("📈 Check the generated CSV file for detailed results")
        print("📊 Performance charts should be displayed")
        print("🧬 Iron Condor strategy is now integrated with OptAlpha!")
    else:
        print("\n❌ Iron Condor OptAlpha example failed")
        print("💡 Check the logs for error details")
        print("🔧 May need to fix OptAlpha integration issues")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
