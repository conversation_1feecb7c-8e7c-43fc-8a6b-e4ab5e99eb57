# Optionstrat - SPX Options Trading System

A comprehensive SPX options trading system with integrated data downloader and genetic algorithm framework.

## 🎯 Project Overview

This project provides a complete solution for SPX options trading research, including:
- **5+ Million SPX Options Records** (24 months of data)
- **Genetic Algorithm Framework** for strategy development
- **Real-time Data Integration** with multiple sources
- **Advanced Analytics** and backtesting capabilities

## 📊 Dataset Summary

- **Records**: 5,014,494 SPX options 5-minute bars
- **Date Range**: August 11, 2023 to July 31, 2025
- **Contracts**: 83,142 unique options contracts
- **Trading Days**: 494 complete trading days
- **Storage**: Efficient parquet format (~500-800 MB)

## 🏗️ Project Structure

```
optionstrat/
├── main.py                 # Main program entry point
├── quantlab/              # Core trading framework
│   ├── gene.py           # Genetic algorithm engine
│   ├── data/             # Data integration modules
│   ├── config/           # Configuration management
│   └── utils/            # Utility functions
├── data/                  # SPX options dataset (495 parquet files)
├── scripts/              # Organized scripts
│   ├── download/         # Data download scripts
│   ├── analysis/         # Analysis and visualization
│   └── utilities/        # Helper utilities
├── docs/                 # Documentation
├── logs/                 # Application logs
└── archive/              # Archived files
```

## 🚀 Quick Start

### Basic Usage
```bash
# Run main program with integrated data
python main.py

# Run example analysis with SPX data
python scripts/analysis/run_example_with_spx_data.py

# Download additional SPX data
python scripts/download/download_spx_optimized.py
```

### Data Analysis
```python
from scripts.analysis.run_example_with_spx_data import load_spx_options_data

# Load the complete dataset
spx_data = load_spx_options_data()
print(f"Loaded {len(spx_data):,} SPX options records")
```

## 📈 Key Features

### Data Integration
- **Multi-source data**: Stock data (yfinance) + SPX options (Polygon.io)
- **Intelligent caching**: Automatic data validation and caching
- **Robust error handling**: Automatic fallback mechanisms

### SPX Options Dataset
- **Complete coverage**: 24 months of high-quality data
- **ATM focus**: 98.4% of records within 2% of ATM
- **High frequency**: 5-minute intraday bars
- **Rich metadata**: Bid/ask spreads, Greeks, volume data

### Trading Framework
- **Genetic algorithms**: Advanced strategy optimization
- **Multiple strategies**: Support for various trading approaches
- **Risk management**: Built-in position sizing and controls
- **Performance analytics**: Comprehensive backtesting metrics

## 🔧 Configuration

Key settings in `.env`:
```bash
POLYGON_API_KEY=your_api_key_here
DATA_DIR=./data
ENABLE_DATA_INTEGRATION=true
DEFAULT_CACHE_ENABLED=true
```

## 📊 Data Quality Metrics

- **SPX Price Range**: $4,117.37 - $6,389.77 (55% appreciation)
- **Options Price Range**: $0.03 - $594.50
- **Average Spread**: $3.64 (tight, liquid markets)
- **Average Volume**: 42 contracts per side
- **Data Completeness**: 100% (no missing trading days)

## 🎯 Trading Strategies

The dataset supports various options strategies:
- **ATM Straddles**: 1.8M+ records, avg price $69.10
- **Iron Condors**: Full strike range coverage
- **Volatility Trading**: 16.2% annualized SPX volatility
- **Custom Strategies**: Genetic algorithm optimization

## 📚 Documentation

- `docs/README_INTEGRATED.md` - Detailed integration guide
- `scripts/analysis/` - Analysis examples and tutorials
- `quantlab/` - Core framework documentation

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

- Check logs in `logs/` directory
- Run analysis examples in `scripts/analysis/`
- Review configuration in `.env`

---

**This is a world-class SPX options dataset ready for quantitative trading research!** 🚀
