#!/usr/bin/env python3
"""
Simple OptAlpha Demo with SPX Options Data

This script demonstrates the integration of our SPX options dataset
with the OptAlpha framework in a simplified way.
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_spx_options_sample():
    """Load a sample of SPX options data for demonstration."""
    
    data_dir = Path("data")
    parquet_files = list(data_dir.glob("spx_options_*.parquet"))
    
    if not parquet_files:
        logger.error("❌ No SPX options parquet files found!")
        return None
    
    logger.info(f"📁 Found {len(parquet_files)} SPX options files")
    
    # Load just the most recent file for demo
    latest_file = sorted(parquet_files)[-1]
    
    try:
        df = pd.read_parquet(latest_file)
        logger.info(f"✅ Loaded sample data from {latest_file.name}: {len(df)} records")
        return df
    except Exception as e:
        logger.error(f"❌ Failed to load {latest_file.name}: {e}")
        return None

def analyze_spx_options_for_optalpha(df):
    """Analyze SPX options data and show OptAlpha integration potential."""
    
    logger.info("📊 Analyzing SPX options data for OptAlpha integration...")
    
    print("\n" + "=" * 60)
    print("📊 SPX OPTIONS DATA FOR OPTALPHA INTEGRATION")
    print("=" * 60)
    
    # Basic dataset info
    print(f"📈 Dataset Overview:")
    print(f"   Records: {len(df):,}")
    print(f"   Date: {df['date'].iloc[0]}")
    print(f"   Unique Contracts: {df['contract'].nunique():,}")
    print(f"   SPX Price Range: ${df['spx_price'].min():.2f} - ${df['spx_price'].max():.2f}")
    
    # Parse contract information for OptAlpha format
    df['contract_type'] = df['contract'].str.extract(r'([CP])')[0].map({'C': 'call', 'P': 'put'})
    df['strike'] = df['contract'].str.extract(r'([CP])(\d{8})')[1].astype(float) / 1000
    df['moneyness'] = df['strike'] / df['spx_price']
    
    # Extract expiration info
    exp_str = df['contract'].str.extract(r'SPX[W]?(\d{6})[CP]')[0]
    df['expiration'] = pd.to_datetime('20' + exp_str, format='%Y%m%d')
    df['dte'] = (df['expiration'] - pd.to_datetime(df['date'])).dt.days
    
    print(f"\n🎯 Contract Analysis:")
    print(f"   Call Options: {len(df[df['contract_type'] == 'call']):,}")
    print(f"   Put Options: {len(df[df['contract_type'] == 'put']):,}")
    print(f"   Strike Range: ${df['strike'].min():.0f} - ${df['strike'].max():.0f}")
    print(f"   DTE Range: {df['dte'].min()} - {df['dte'].max()} days")
    
    # Moneyness analysis
    atm_options = df[(df['moneyness'] >= 0.98) & (df['moneyness'] <= 1.02)]
    otm_calls = df[(df['contract_type'] == 'call') & (df['moneyness'] > 1.02)]
    otm_puts = df[(df['contract_type'] == 'put') & (df['moneyness'] < 0.98)]
    
    print(f"\n💎 Moneyness Distribution:")
    print(f"   ATM Options (98-102%): {len(atm_options):,}")
    print(f"   OTM Calls (>102%): {len(otm_calls):,}")
    print(f"   OTM Puts (<98%): {len(otm_puts):,}")
    
    # Liquidity analysis
    print(f"\n💧 Liquidity Analysis:")
    print(f"   Average Bid Size: {df['bid_size'].mean():.0f}")
    print(f"   Average Ask Size: {df['ask_size'].mean():.0f}")
    print(f"   Average Spread: ${df['spread'].mean():.2f}")
    print(f"   Spread as % of Price: {(df['spread'] / df['close']).mean() * 100:.2f}%")
    
    return df

def demonstrate_optalpha_strategies(df):
    """Demonstrate potential OptAlpha strategies with SPX data."""
    
    print(f"\n🧬 OPTALPHA STRATEGY EXAMPLES")
    print("=" * 45)
    
    # Strategy 1: ATM Straddle
    print(f"\n📊 Strategy 1: ATM Straddle")
    atm_calls = df[(df['contract_type'] == 'call') & (df['moneyness'] >= 0.99) & (df['moneyness'] <= 1.01)]
    atm_puts = df[(df['contract_type'] == 'put') & (df['moneyness'] >= 0.99) & (df['moneyness'] <= 1.01)]
    
    if not atm_calls.empty and not atm_puts.empty:
        avg_call_price = atm_calls['close'].mean()
        avg_put_price = atm_puts['close'].mean()
        straddle_cost = avg_call_price + avg_put_price
        
        print(f"   ATM Call Price: ${avg_call_price:.2f}")
        print(f"   ATM Put Price: ${avg_put_price:.2f}")
        print(f"   Straddle Cost: ${straddle_cost:.2f}")
        print(f"   Breakeven Points: ${df['spx_price'].iloc[0] - straddle_cost:.0f} / ${df['spx_price'].iloc[0] + straddle_cost:.0f}")
    
    # Strategy 2: Iron Condor
    print(f"\n📊 Strategy 2: Iron Condor")
    otm_puts = df[(df['contract_type'] == 'put') & (df['moneyness'] >= 0.90) & (df['moneyness'] <= 0.95)]
    otm_calls = df[(df['contract_type'] == 'call') & (df['moneyness'] >= 1.05) & (df['moneyness'] <= 1.10)]
    
    if not otm_puts.empty and not otm_calls.empty:
        put_credit = otm_puts['close'].mean()
        call_credit = otm_calls['close'].mean()
        total_credit = put_credit + call_credit
        
        print(f"   OTM Put Credit: ${put_credit:.2f}")
        print(f"   OTM Call Credit: ${call_credit:.2f}")
        print(f"   Total Credit: ${total_credit:.2f}")
        print(f"   Max Profit: ${total_credit:.2f}")
    
    # Strategy 3: Covered Call
    print(f"\n📊 Strategy 3: Covered Call Analysis")
    otm_calls = df[(df['contract_type'] == 'call') & (df['moneyness'] >= 1.02) & (df['moneyness'] <= 1.05)]
    
    if not otm_calls.empty:
        call_premium = otm_calls['close'].mean()
        strike_price = otm_calls['strike'].mean()
        
        print(f"   Call Premium: ${call_premium:.2f}")
        print(f"   Strike Price: ${strike_price:.0f}")
        print(f"   Yield if Called: {((strike_price - df['spx_price'].iloc[0] + call_premium) / df['spx_price'].iloc[0]) * 100:.2f}%")

def show_optalpha_integration_format(df):
    """Show how to format data for OptAlpha integration."""
    
    print(f"\n🔧 OPTALPHA DATA FORMAT EXAMPLE")
    print("=" * 40)
    
    # Create sample OptAlpha format
    sample_data = df.head(10).copy()
    
    # Format for OptAlpha
    optalpha_format = pd.DataFrame({
        'optionroot': sample_data['contract'],
        'underlying': 'SPX',
        'underlying_last': sample_data['spx_price'],
        'type': sample_data['contract'].str.extract(r'([CP])')[0].map({'C': 'call', 'P': 'put'}),
        'quotedate': pd.to_datetime(sample_data['date']),
        'strike': sample_data['contract'].str.extract(r'([CP])(\d{8})')[1].astype(float) / 1000,
        'last': sample_data['close'],
        'volume': sample_data['bid_size'] + sample_data['ask_size'],
        'openinterest': (sample_data['bid_size'] + sample_data['ask_size']) * 10,
        'dte': 30  # Simplified for demo
    })
    
    print("📋 Sample OptAlpha Format:")
    print(optalpha_format[['underlying', 'type', 'strike', 'last', 'volume', 'dte']].head())
    
    print(f"\n🎯 Integration Points:")
    print(f"   • {len(df):,} records ready for OptAlpha")
    print(f"   • Complete bid/ask/spread data")
    print(f"   • Multiple expiration dates")
    print(f"   • Full strike range coverage")
    print(f"   • High-frequency 5-minute data")

def demonstrate_genetic_algorithm_potential(df):
    """Show potential for genetic algorithm optimization."""
    
    print(f"\n🧬 GENETIC ALGORITHM OPTIMIZATION POTENTIAL")
    print("=" * 50)
    
    print(f"🎯 Strategy Parameters for Optimization:")
    print(f"   • Strike Selection: {df['strike'].nunique()} unique strikes")
    print(f"   • Expiration Dates: Multiple DTE options")
    print(f"   • Entry/Exit Timing: 5-minute granularity")
    print(f"   • Position Sizing: Based on liquidity metrics")
    print(f"   • Risk Management: Spread-based stop losses")
    
    print(f"\n📊 Optimization Dimensions:")
    print(f"   • Moneyness Range: 0.80 to 1.20 ({(df['moneyness'].max() - df['moneyness'].min()):.2f} range)")
    print(f"   • Time Decay: Multiple DTE buckets")
    print(f"   • Volatility Regime: Based on SPX price movements")
    print(f"   • Liquidity Filters: Bid-ask spread thresholds")
    
    print(f"\n🎲 Genetic Algorithm Fitness Functions:")
    print(f"   • Sharpe Ratio maximization")
    print(f"   • Maximum drawdown minimization")
    print(f"   • Win rate optimization")
    print(f"   • Risk-adjusted returns")

def main():
    """Main demonstration function."""
    
    print("🚀 OptAlpha Integration with SPX Options Data")
    print("=" * 55)
    
    # Load sample SPX options data
    spx_data = load_spx_options_sample()
    
    if spx_data is None:
        print("❌ No SPX options data available")
        print("💡 Make sure you have downloaded SPX options data first")
        return False
    
    # Analyze the data for OptAlpha integration
    analyzed_data = analyze_spx_options_for_optalpha(spx_data)
    
    # Demonstrate potential strategies
    demonstrate_optalpha_strategies(analyzed_data)
    
    # Show OptAlpha integration format
    show_optalpha_integration_format(analyzed_data)
    
    # Show genetic algorithm potential
    demonstrate_genetic_algorithm_potential(analyzed_data)
    
    # Summary
    print(f"\n🎉 OPTALPHA INTEGRATION DEMONSTRATION COMPLETE!")
    print("=" * 55)
    print(f"✅ Analyzed {len(spx_data):,} SPX options records")
    print(f"📊 Demonstrated multiple trading strategies")
    print(f"🧬 Showed genetic algorithm optimization potential")
    print(f"🔧 Provided OptAlpha data format examples")
    
    print(f"\n📋 Next Steps for Full Integration:")
    print(f"   • Implement complete OptAlpha subclass")
    print(f"   • Add real-time data streaming")
    print(f"   • Integrate with genetic algorithm framework")
    print(f"   • Implement backtesting with full dataset")
    print(f"   • Add risk management and position sizing")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
