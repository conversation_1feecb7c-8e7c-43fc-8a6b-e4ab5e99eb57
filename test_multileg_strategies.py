#!/usr/bin/env python3
"""
Test Multi-Leg Options Strategies

This script demonstrates how to use the extended OptAlpha framework
to backtest complex multi-leg options strategies like Iron Condors,
Butterflies, Calendars, and Spreads using our SPX options data.
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import logging

# Add quantlab to path
sys.path.append('quantlab')

from strategies.iron_condor import IronCondorStrategy
from strategies.butterfly import ButterflyStrategy
from strategies.calendar import CalendarStrategy
from strategies.spread import SpreadStrategy

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MultiLegStrategyTester:
    """Test harness for multi-leg options strategies."""
    
    def __init__(self, data_dir="data"):
        self.data_dir = Path(data_dir)
        self.spx_data = None
        
    def load_spx_data(self):
        """Load SPX options data for testing."""
        
        # Try to load the master file first
        master_file = self.data_dir / "spx_options_24months_optimized.parquet"
        
        if master_file.exists():
            try:
                self.spx_data = pd.read_parquet(master_file)
                logger.info(f"✅ Loaded master file: {len(self.spx_data):,} records")
                return True
            except Exception as e:
                logger.warning(f"⚠️  Failed to load master file: {e}")
        
        # Fallback to individual parquet files
        parquet_files = list(self.data_dir.glob("spx_options_*.parquet"))
        
        if not parquet_files:
            logger.error("❌ No SPX options parquet files found!")
            return False
        
        # Load recent files for testing
        dfs = []
        for pf in sorted(parquet_files)[-10:]:  # Last 10 days for testing
            try:
                df = pd.read_parquet(pf)
                if not df.empty:
                    dfs.append(df)
            except Exception as e:
                logger.warning(f"⚠️  Failed to load {pf.name}: {e}")
        
        if not dfs:
            return False
        
        self.spx_data = pd.concat(dfs, ignore_index=True)
        logger.info(f"✅ Loaded combined data: {len(self.spx_data):,} records")
        
        return True
    
    def prepare_data_for_optalpha(self):
        """Prepare SPX data in OptAlpha format."""
        
        if self.spx_data is None:
            logger.error("No SPX data loaded")
            return None
        
        logger.info("🔄 Preparing data for OptAlpha format...")
        
        # Parse contract information
        self.spx_data['contract_type'] = self.spx_data['contract'].str.extract(r'([CP])')[0]
        self.spx_data['strike'] = self.spx_data['contract'].str.extract(r'([CP])(\d{8})')[1].astype(float) / 1000
        
        # Map to OptAlpha format
        optalpha_data = pd.DataFrame({
            'optionroot': self.spx_data['contract'],
            'underlying': 'SPX',
            'underlying_last': self.spx_data['spx_price'],
            'type': self.spx_data['contract_type'].map({'C': 'call', 'P': 'put'}),
            'quotedate': pd.to_datetime(self.spx_data['date']),
            'strike': self.spx_data['strike'],
            'bid': self.spx_data['bid'],
            'ask': self.spx_data['ask'],
            'last': self.spx_data['close'],
            'volume': self.spx_data['bid_size'] + self.spx_data['ask_size'],
            'openinterest': (self.spx_data['bid_size'] + self.spx_data['ask_size']) * 10,
            'dte': 30  # Simplified for demo
        })
        
        # Calculate additional fields
        optalpha_data['spread'] = optalpha_data['ask'] - optalpha_data['bid']
        optalpha_data['mid'] = (optalpha_data['bid'] + optalpha_data['ask']) / 2
        optalpha_data['moneyness'] = optalpha_data['strike'] / optalpha_data['underlying_last']
        
        # Fill missing last prices with mid prices
        optalpha_data['last'] = optalpha_data['last'].fillna(optalpha_data['mid'])
        
        logger.info(f"✅ Prepared OptAlpha data: {len(optalpha_data):,} records")
        
        return optalpha_data
    
    def test_iron_condor_strategy(self, data):
        """Test Iron Condor strategy."""
        
        print(f"\n🧬 TESTING IRON CONDOR STRATEGY")
        print("=" * 45)
        
        try:
            # Initialize Iron Condor strategy
            iron_condor = IronCondorStrategy(
                wing_width=50,
                put_otm_distance=0.05,
                call_otm_distance=0.05,
                target_dte=30,
                profit_target=0.5,
                stop_loss=2.0,
                time_stop=7
            )
            
            print(f"✅ Iron Condor strategy initialized")
            print(f"   Strategy: {iron_condor.strategy_name}")
            print(f"   Legs: {len(iron_condor.legs)}")
            print(f"   Wing Width: ${iron_condor.wing_width}")
            
            # Test with sample data
            sample_date = data['quotedate'].dt.date.iloc[0]
            sample_data = data[data['quotedate'].dt.date == sample_date]
            underlying_price = sample_data['underlying_last'].iloc[0]
            
            print(f"\n📊 Testing with sample data:")
            print(f"   Date: {sample_date}")
            print(f"   SPX Price: ${underlying_price:.2f}")
            print(f"   Available Options: {len(sample_data):,}")
            
            # Find Iron Condor contracts
            contracts = iron_condor.find_option_contracts(
                datetime.combine(sample_date, datetime.min.time()),
                underlying_price,
                sample_data
            )
            
            print(f"\n🎯 Iron Condor Legs Found: {len(contracts)}")
            for leg_name, contract in contracts.items():
                print(f"   {leg_name}: Strike ${contract['strike']:.0f}, Price ${contract['last']:.2f}")
            
            # Calculate theoretical P&L
            if len(contracts) == 4:
                strikes = {
                    'short_put': contracts['leg_0_sell_put']['strike'],
                    'long_put': contracts['leg_1_buy_put']['strike'],
                    'short_call': contracts['leg_2_sell_call']['strike'],
                    'long_call': contracts['leg_3_buy_call']['strike']
                }
                
                premiums = {
                    'short_put': contracts['leg_0_sell_put']['last'],
                    'long_put': contracts['leg_1_buy_put']['last'],
                    'short_call': contracts['leg_2_sell_call']['last'],
                    'long_call': contracts['leg_3_buy_call']['last']
                }
                
                pnl_analysis = iron_condor.calculate_iron_condor_pnl(
                    underlying_price, strikes, premiums
                )
                
                print(f"\n💰 Iron Condor P&L Analysis:")
                print(f"   Credit Received: ${pnl_analysis['credit_received']:.2f}")
                print(f"   Max Profit: ${pnl_analysis['max_profit']:.2f}")
                print(f"   Max Loss: ${pnl_analysis['max_loss']:.2f}")
                
                # Calculate breakevens
                lower_be, upper_be = iron_condor.get_breakeven_points(strikes, pnl_analysis['credit_received'])
                print(f"   Breakevens: ${lower_be:.2f} / ${upper_be:.2f}")
                print(f"   Profit Zone: {upper_be - lower_be:.2f} points")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Iron Condor test failed: {e}")
            return False
    
    def test_butterfly_strategy(self, data):
        """Test Butterfly strategy."""
        
        print(f"\n🦋 TESTING BUTTERFLY STRATEGY")
        print("=" * 40)
        
        try:
            # Initialize Butterfly strategy
            butterfly = ButterflyStrategy(
                butterfly_type='call',
                wing_width=50,
                target_dte=30,
                profit_target=0.75,
                stop_loss=2.0,
                time_stop=7
            )
            
            print(f"✅ Butterfly strategy initialized")
            print(f"   Strategy: {butterfly.strategy_name}")
            print(f"   Type: {butterfly.butterfly_type}")
            print(f"   Legs: {len(butterfly.legs)}")
            
            # Test with sample data
            sample_date = data['quotedate'].dt.date.iloc[0]
            sample_data = data[data['quotedate'].dt.date == sample_date]
            underlying_price = sample_data['underlying_last'].iloc[0]
            
            print(f"\n📊 Testing with sample data:")
            print(f"   Date: {sample_date}")
            print(f"   SPX Price: ${underlying_price:.2f}")
            
            # Find Butterfly strikes
            strikes = butterfly.find_butterfly_strikes(underlying_price, sample_data)
            
            print(f"\n🎯 Butterfly Strikes:")
            for strike_name, strike_price in strikes.items():
                print(f"   {strike_name}: ${strike_price:.0f}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Butterfly test failed: {e}")
            return False
    
    def test_spread_strategy(self, data):
        """Test Vertical Spread strategy."""
        
        print(f"\n📈 TESTING VERTICAL SPREAD STRATEGY")
        print("=" * 45)
        
        try:
            # Initialize Bull Call Spread
            spread = SpreadStrategy(
                spread_type='bull_call',
                strike_width=50,
                otm_distance=0.02,
                target_dte=30,
                profit_target=0.5,
                stop_loss=2.0,
                time_stop=7
            )
            
            print(f"✅ Spread strategy initialized")
            print(f"   Strategy: {spread.strategy_name}")
            print(f"   Type: {spread.spread_type}")
            print(f"   Credit Spread: {spread.is_credit_spread}")
            
            # Test with sample data
            sample_date = data['quotedate'].dt.date.iloc[0]
            sample_data = data[data['quotedate'].dt.date == sample_date]
            underlying_price = sample_data['underlying_last'].iloc[0]
            
            print(f"\n📊 Testing with sample data:")
            print(f"   Date: {sample_date}")
            print(f"   SPX Price: ${underlying_price:.2f}")
            
            # Find spread strikes
            strikes = spread.find_spread_strikes(underlying_price, sample_data)
            
            print(f"\n🎯 Spread Strikes:")
            for strike_name, strike_price in strikes.items():
                print(f"   {strike_name}: ${strike_price:.0f}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Spread test failed: {e}")
            return False
    
    def run_all_tests(self):
        """Run all strategy tests."""
        
        print("🚀 Multi-Leg Options Strategy Testing")
        print("=" * 50)
        
        # Load data
        if not self.load_spx_data():
            print("❌ Failed to load SPX data")
            return False
        
        # Prepare data
        optalpha_data = self.prepare_data_for_optalpha()
        if optalpha_data is None:
            print("❌ Failed to prepare data")
            return False
        
        # Run tests
        results = {}
        
        results['iron_condor'] = self.test_iron_condor_strategy(optalpha_data)
        results['butterfly'] = self.test_butterfly_strategy(optalpha_data)
        results['spread'] = self.test_spread_strategy(optalpha_data)
        
        # Summary
        print(f"\n🎉 TESTING COMPLETE!")
        print("=" * 30)
        
        for strategy, success in results.items():
            status = "✅ PASSED" if success else "❌ FAILED"
            print(f"   {strategy.replace('_', ' ').title()}: {status}")
        
        total_passed = sum(results.values())
        print(f"\n📊 Results: {total_passed}/{len(results)} strategies passed")
        
        return all(results.values())

def main():
    """Main testing function."""
    
    tester = MultiLegStrategyTester()
    success = tester.run_all_tests()
    
    if success:
        print(f"\n🎯 All multi-leg strategy tests passed!")
        print(f"📋 Framework is ready for backtesting:")
        print(f"   • Iron Condor strategies")
        print(f"   • Butterfly strategies") 
        print(f"   • Calendar spreads")
        print(f"   • Vertical spreads")
        print(f"   • Custom multi-leg strategies")
    else:
        print(f"\n⚠️  Some tests failed. Check logs for details.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
