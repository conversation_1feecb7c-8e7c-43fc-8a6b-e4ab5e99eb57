import pytz
import yfinance
import requests
import threading
import pandas as pd
import logging
from datetime import datetime
from bs4 import BeautifulSoup
from quantlab.utils import timeme
from quantlab.utils import save_pickle, load_pickle
from quantlab.utils import Portfolio

# Import the new data integration manager
try:
    from quantlab.data.data_integration import create_data_manager
    DATA_INTEGRATION_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Data integration not available: {e}")
    DATA_INTEGRATION_AVAILABLE = False

# Configure logging
try:
    from quantlab.utils.logging_config import setup_optionstrat_logging, get_contextual_logger
    setup_optionstrat_logging()
    logger = get_contextual_logger(__name__, "MainProgram")
except ImportError:
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)

def get_sp500_tickers():
    res = requests.get("https://en.wikipedia.org/wiki/List_of_S%26P_500_companies")
    soup = BeautifulSoup(res.content,'html')
    table = soup.find_all('table')[0] 
    df = pd.read_html(str(table))
    tickers = list(df[0].Symbol)
    return tickers

def get_history(ticker, period_start, period_end, granularity="1d", tries=0):
    try:
        df = yfinance.Ticker(ticker).history(
            start=period_start,
            end=period_end,
            interval=granularity,
            auto_adjust=True
        ).reset_index()
    except Exception as err:
        if tries < 5:
            return get_history(ticker, period_start, period_end, granularity, tries+1)
        return pd.DataFrame()
    
    df = df.rename(columns={
        "Date":"datetime",
        "Open":"open",
        "High":"high",
        "Low":"low",
        "Close":"close",
        "Volume":"volume"
    })
    if df.empty:
        return pd.DataFrame()
    df.datetime = pd.DatetimeIndex(df.datetime.dt.date).tz_localize(pytz.utc)
    df = df.drop(columns=["Dividends", "Stock Splits"])
    df = df.set_index("datetime",drop=True)
    return df

def get_histories(tickers, period_starts,period_ends, granularity="1d"):
    dfs = [None]*len(tickers)
    def _helper(i):
        print(tickers[i])
        df = get_history(
            tickers[i],
            period_starts[i], 
            period_ends[i], 
            granularity=granularity
        )
        dfs[i] = df
    threads = [threading.Thread(target=_helper,args=(i,)) for i in range(len(tickers))]
    [thread.start() for thread in threads]
    [thread.join() for thread in threads]
    tickers = [tickers[i] for i in range(len(tickers)) if not dfs[i].empty]
    dfs = [df for df in dfs if not df.empty]
    return tickers, dfs

def get_ticker_dfs(start, end):
    """Legacy function for backward compatibility."""
    from quantlab.utils import load_pickle, save_pickle
    try:
        tickers, ticker_dfs = load_pickle("dataset.obj")
    except Exception as err:
        tickers = get_sp500_tickers()
        starts = [start] * len(tickers)
        ends = [end] * len(tickers)
        tickers, dfs = get_histories(tickers, starts, ends, granularity="1d")
        ticker_dfs = {ticker: df for ticker, df in zip(tickers, dfs)}
        save_pickle("dataset.obj", (tickers, ticker_dfs))
    return tickers, ticker_dfs


def get_integrated_data(start, end, tickers=None, include_spx_options=False,
                       spx_download_mode="incremental", use_cache=True):
    """
    Get data using the integrated data manager with support for multiple data sources.

    Args:
        start: Start date for data
        end: End date for data
        tickers: List of tickers (if None, uses S&P 500)
        include_spx_options: Whether to include SPX options data
        spx_download_mode: "full" or "incremental" for SPX options
        use_cache: Whether to use cached data

    Returns:
        Dictionary containing requested data
    """
    if not DATA_INTEGRATION_AVAILABLE:
        logger.warning("Data integration not available, falling back to legacy method")
        if tickers is None:
            tickers = get_sp500_tickers()
        valid_tickers, ticker_dfs = get_ticker_dfs(start, end)
        return {
            'stock_data': {
                'tickers': valid_tickers,
                'dataframes': ticker_dfs
            },
            'spx_options_data': None
        }

    try:
        # Create data manager
        data_manager = create_data_manager(cache_enabled=use_cache)

        # Get tickers if not provided
        if tickers is None:
            tickers = get_sp500_tickers()

        # Get combined dataset
        logger.info(f"Fetching integrated data from {start} to {end}")
        dataset = data_manager.get_combined_dataset(
            tickers=tickers,
            start_date=start,
            end_date=end,
            include_spx_options=include_spx_options,
            spx_download_mode=spx_download_mode
        )

        return dataset

    except Exception as e:
        logger.error(f"Error in integrated data fetch: {e}")
        logger.info("Falling back to legacy data method")

        # Fallback to legacy method
        if tickers is None:
            tickers = get_sp500_tickers()
        valid_tickers, ticker_dfs = get_ticker_dfs(start, end)
        return {
            'stock_data': {
                'tickers': valid_tickers,
                'dataframes': ticker_dfs
            },
            'spx_options_data': None
        }

import numpy as np
import matplotlib.pyplot as plt
from pprint import pprint
from quantlab.gene import GeneticAlpha
from quantlab.gene import Gene
def main(use_integrated_data=True, include_spx_options=False):
    """
    Main function with support for both legacy and integrated data approaches.

    Args:
        use_integrated_data: Whether to use the new integrated data manager
        include_spx_options: Whether to include SPX options data (requires integrated data)
    """
    period_start = datetime(2000, 1, 1, tzinfo=pytz.utc)
    period_end = datetime(2023, 1, 1, tzinfo=pytz.utc)

    logger.info(f"Starting main analysis from {period_start} to {period_end}")
    logger.info(f"Using integrated data: {use_integrated_data}")
    logger.info(f"Including SPX options: {include_spx_options}")

    if use_integrated_data and DATA_INTEGRATION_AVAILABLE:
        # Use the new integrated data approach
        logger.info("Using integrated data manager")
        dataset = get_integrated_data(
            start=period_start,
            end=period_end,
            include_spx_options=include_spx_options,
            spx_download_mode="incremental"
        )

        # Extract stock data
        stock_data = dataset['stock_data']
        tickers = stock_data['tickers'][:50]  # Limit to first 50
        ticker_dfs = {ticker: stock_data['dataframes'][ticker] for ticker in tickers}

        # Log SPX options data if available
        spx_data = dataset.get('spx_options_data')
        if spx_data is not None:
            logger.info(f"SPX options data loaded: {len(spx_data)} records")
            logger.info(f"SPX data columns: {list(spx_data.columns)}")
            logger.info(f"SPX date range: {spx_data['date'].min()} to {spx_data['date'].max()}")
        else:
            logger.info("No SPX options data available")

    else:
        # Use legacy approach
        logger.info("Using legacy data approach")
        tickers, ticker_dfs = get_ticker_dfs(start=period_start, end=period_end)
        tickers = tickers[:50]
        ticker_dfs = {ticker: ticker_dfs[ticker] for ticker in tickers}

    # Load alternative data if available
    try:
        _, altdata = load_pickle("altdata.obj")
        for ticker in tickers:
            if ticker in altdata:
                ticker_dfs.update({
                    ticker + "_" + k: v
                    for k, v in altdata[ticker].to_dict(orient="series").items()
                })
        logger.info("Alternative data loaded successfully")
    except Exception as e:
        logger.warning(f"Alternative data not available: {e}")

    logger.info(f"Available data keys: {len(ticker_dfs)} datasets")

    # Run genetic algorithm analysis
    from quantlab.gene import GeneticAlpha, Gene

    # Example genetic algorithm strategies
    strategies = {
        "eps_difference": "div(epsDifference,std_12(epsDifference))",
        # Uncomment to try other strategies:
        # "volume_momentum": "ls_25/75(neg(mean_12(cszscre(div(mult(volume,minus(minus(close,low),minus(high,close))),minus(high,low))))))",
        # "price_momentum": "neg(mean_12(minus(const_1,div(open,close))))",
        # "trend_following": "plus(ite(gt(mean_10(close),mean_50(close)),const_1,const_0),ite(gt(mean_20(close),mean_100(close)),const_1,const_0),ite(gt(mean_50(close),mean_200(close)),const_1,const_0))"
    }

    for strategy_name, strategy_gene in strategies.items():
        logger.info(f"Running strategy: {strategy_name}")
        try:
            g1 = Gene.str_to_gene(strategy_gene)
            alpha1 = GeneticAlpha(
                insts=tickers,
                dfs=ticker_dfs,
                start=period_start,
                end=period_end,
                genome=g1
            )
            df1 = alpha1.run_simulation()
            perf_stats = alpha1.get_perf_stats(plot=True)

            logger.info(f"Strategy {strategy_name} completed")
            logger.info(f"Performance summary: {perf_stats}")

            # Save results
            save_pickle(f"results_{strategy_name}.obj", {
                'simulation_df': df1,
                'performance_stats': perf_stats,
                'strategy_gene': strategy_gene
            })

        except Exception as e:
            logger.error(f"Error running strategy {strategy_name}: {e}")

    logger.info("Analysis completed")
       
if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Optionstrat Program with Integrated Data Sources")
    parser.add_argument('--legacy-data', action='store_true',
                       help='Use legacy yfinance-only data approach')
    parser.add_argument('--include-spx-options', action='store_true',
                       help='Include SPX options data (requires integrated data manager)')
    parser.add_argument('--data-summary', action='store_true',
                       help='Show data source summary and exit')

    args = parser.parse_args()

    # Show data summary if requested
    if args.data_summary:
        if DATA_INTEGRATION_AVAILABLE:
            try:
                data_manager = create_data_manager()
                summary = data_manager.get_data_summary()
                print("\n=== Data Source Summary ===")
                print(f"SPX Downloader Available: {summary['spx_downloader_available']}")
                print(f"Cache Enabled: {summary['cache_enabled']}")
                print(f"Data Directory: {summary['data_directory']}")
                print(f"Cached Files: {summary['cached_files']}")
                if 'spx_metadata' in summary:
                    print(f"SPX Metadata: {summary['spx_metadata']}")
                print("=" * 30)
            except Exception as e:
                print(f"Error getting data summary: {e}")
        else:
            print("Data integration not available - only legacy yfinance data supported")
        exit(0)

    # Run main analysis
    use_integrated = not args.legacy_data
    include_spx = args.include_spx_options

    if include_spx and not DATA_INTEGRATION_AVAILABLE:
        print("Warning: SPX options requested but data integration not available")
        include_spx = False

    main(use_integrated_data=use_integrated, include_spx_options=include_spx)
