
def save_daily_parquet_fixed(day_df, date_str, data_dir):
    """Fixed parquet saving function that avoids pandas.period issues."""
    import pandas as pd
    import pyarrow as pa
    import pyarrow.parquet as pq
    from pathlib import Path
    import logging
    
    logger = logging.getLogger(__name__)
    daily_file = Path(data_dir) / f"spx_options_{date_str.replace('-', '')}.parquet"
    
    try:
        # Clean the dataframe
        df_clean = day_df.copy()
        
        # Convert all datetime-like columns to strings
        for col in df_clean.columns:
            if 'datetime' in col or 'time' in col:
                df_clean[col] = df_clean[col].astype(str)
        
        # Ensure numeric columns are float64
        numeric_cols = ['open', 'high', 'low', 'close', 'bid', 'ask', 'spread', 'bid_size', 'ask_size', 'spx_price']
        for col in numeric_cols:
            if col in df_clean.columns:
                df_clean[col] = pd.to_numeric(df_clean[col], errors='coerce').astype('float64')
        
        # Ensure string columns are string type
        string_cols = ['contract', 'date']
        for col in string_cols:
            if col in df_clean.columns:
                df_clean[col] = df_clean[col].astype('string')
        
        # Create explicit schema
        schema_fields = []
        for col in df_clean.columns:
            if col in numeric_cols:
                schema_fields.append(pa.field(col, pa.float64()))
            else:
                schema_fields.append(pa.field(col, pa.string()))
        
        schema = pa.schema(schema_fields)
        
        # Convert to pyarrow table and save
        table = pa.Table.from_pandas(df_clean, schema=schema, preserve_index=False)
        pq.write_table(table, daily_file, compression='snappy')
        
        file_size_kb = daily_file.stat().st_size / 1024
        logger.info(f"  💾 ✅ Fixed parquet save: {daily_file} ({file_size_kb:.1f} KB)")
        return True
        
    except Exception as e:
        logger.warning(f"  ⚠️  Fixed parquet save failed: {e}")
        
        # Fallback to CSV
        csv_file = daily_file.with_suffix('.csv')
        try:
            day_df.to_csv(csv_file, index=False)
            logger.info(f"  💾 📄 CSV fallback: {csv_file}")
            return True
        except Exception as csv_e:
            logger.error(f"  ❌ CSV fallback failed: {csv_e}")
            return False
