#!/usr/bin/env python3
"""Test parquet files"""

import pandas as pd
from pathlib import Path

def test_parquet_files():
    data_dir = Path("data")
    parquet_files = list(data_dir.glob("spx_options_*.parquet"))
    
    print(f"🔍 Found {len(parquet_files)} parquet files")
    
    for pf in parquet_files:
        try:
            df = pd.read_parquet(pf)
            print(f"✅ {pf.name}: {len(df)} records")
            if len(df) > 0:
                print(f"   Columns: {list(df.columns)}")
                print(f"   Sample: {df.iloc[0]['contract']} at {df.iloc[0]['datetime']}")
        except Exception as e:
            print(f"❌ {pf.name}: Error - {e}")

if __name__ == "__main__":
    test_parquet_files()
