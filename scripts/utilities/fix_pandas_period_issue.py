#!/usr/bin/env python3
"""
Fix pandas.period type extension issue

This script addresses the "A type extension with name pandas.period already defined" error
by resetting the pandas/pyarrow type registry and providing a clean conversion function.
"""

import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq
import numpy as np
from pathlib import Path
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def reset_pandas_extensions():
    """Reset pandas extension registry to avoid conflicts."""
    try:
        # Clear any existing pandas extension types from pyarrow
        import pyarrow.pandas_compat
        
        # This is a workaround for the pandas.period extension conflict
        logger.info("🔧 Resetting pandas extension registry...")
        
        # Force reload of pandas compatibility
        import importlib
        importlib.reload(pyarrow.pandas_compat)
        
        logger.info("✅ Extension registry reset")
        return True
        
    except Exception as e:
        logger.warning(f"⚠️  Could not reset extensions: {e}")
        return False

def clean_dataframe_for_parquet(df):
    """Clean DataFrame to avoid pandas/pyarrow type conflicts."""
    
    df_clean = df.copy()
    
    # Convert all datetime columns to strings
    for col in df_clean.columns:
        if df_clean[col].dtype == 'datetime64[ns]' or 'datetime' in str(df_clean[col].dtype):
            logger.debug(f"Converting datetime column {col} to string")
            df_clean[col] = df_clean[col].astype(str)
        
        # Convert any period types to strings
        elif hasattr(df_clean[col].dtype, 'name') and 'period' in df_clean[col].dtype.name:
            logger.debug(f"Converting period column {col} to string")
            df_clean[col] = df_clean[col].astype(str)
        
        # Convert time objects to strings
        elif df_clean[col].dtype == 'object':
            # Check if it contains time objects
            sample = df_clean[col].dropna().iloc[0] if not df_clean[col].dropna().empty else None
            if sample and hasattr(sample, 'strftime'):
                logger.debug(f"Converting time column {col} to string")
                df_clean[col] = df_clean[col].astype(str)
    
    # Ensure numeric columns are proper types
    numeric_columns = ['open', 'high', 'low', 'close', 'bid', 'ask', 'spread', 'bid_size', 'ask_size', 'spx_price']
    for col in numeric_columns:
        if col in df_clean.columns:
            df_clean[col] = pd.to_numeric(df_clean[col], errors='coerce').astype('float64')
    
    # Ensure string columns are proper string type
    string_columns = ['contract', 'date', 'datetime', 'time_est']
    for col in string_columns:
        if col in df_clean.columns:
            df_clean[col] = df_clean[col].astype('string')
    
    return df_clean

def safe_parquet_save(df, filepath, compression='snappy'):
    """Safely save DataFrame to parquet with error handling."""
    
    try:
        # Reset extensions first
        reset_pandas_extensions()
        
        # Clean the dataframe
        df_clean = clean_dataframe_for_parquet(df)
        
        # Create explicit schema to avoid type inference issues
        schema_fields = []
        
        for col in df_clean.columns:
            if col in ['open', 'high', 'low', 'close', 'bid', 'ask', 'spread', 'bid_size', 'ask_size', 'spx_price']:
                schema_fields.append(pa.field(col, pa.float64()))
            else:
                schema_fields.append(pa.field(col, pa.string()))
        
        schema = pa.schema(schema_fields)
        
        # Convert to pyarrow table with explicit schema
        table = pa.Table.from_pandas(df_clean, schema=schema, preserve_index=False)
        
        # Write parquet file
        pq.write_table(table, filepath, compression=compression)
        
        logger.info(f"✅ Successfully saved parquet: {filepath}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Parquet save failed: {e}")
        
        # Fallback to CSV
        try:
            csv_path = str(filepath).replace('.parquet', '.csv')
            df.to_csv(csv_path, index=False)
            logger.info(f"💾 Saved as CSV fallback: {csv_path}")
            return True
        except Exception as csv_e:
            logger.error(f"❌ CSV fallback also failed: {csv_e}")
            return False

def test_fix():
    """Test the fix with sample data."""
    
    logger.info("🧪 Testing pandas.period fix...")
    
    # Create sample data similar to what's causing the issue
    sample_data = {
        'open': [100.0, 101.0, 102.0],
        'high': [105.0, 106.0, 107.0],
        'low': [99.0, 100.0, 101.0],
        'close': [104.0, 105.0, 106.0],
        'bid': [103.5, 104.5, 105.5],
        'ask': [104.5, 105.5, 106.5],
        'spread': [1.0, 1.0, 1.0],
        'bid_size': [10, 15, 20],
        'ask_size': [12, 18, 22],
        'datetime': ['2025-07-31 09:30:00-0400', '2025-07-31 09:35:00-0400', '2025-07-31 09:40:00-0400'],
        'time_est': ['09:30:00', '09:35:00', '09:40:00'],
        'date': ['2025-07-31', '2025-07-31', '2025-07-31'],
        'contract': ['O:SPXW250801C06400000', 'O:SPXW250801C06400000', 'O:SPXW250801C06400000'],
        'spx_price': [6400.0, 6401.0, 6402.0]
    }
    
    df = pd.DataFrame(sample_data)
    
    # Test the fix
    test_file = Path("test_fix.parquet")
    
    if safe_parquet_save(df, test_file):
        logger.info("✅ Fix test successful!")
        
        # Verify we can read it back
        try:
            df_read = pd.read_parquet(test_file)
            logger.info(f"✅ Read back {len(df_read)} records successfully")
            
            # Clean up
            test_file.unlink()
            logger.info("🧹 Test file cleaned up")
            
            return True
        except Exception as e:
            logger.error(f"❌ Could not read back test file: {e}")
            return False
    else:
        logger.error("❌ Fix test failed")
        return False

def create_fixed_save_function():
    """Create a fixed save function that can be used in the download script."""
    
    function_code = '''
def save_daily_parquet_fixed(day_df, date_str, data_dir):
    """Fixed parquet saving function that avoids pandas.period issues."""
    import pandas as pd
    import pyarrow as pa
    import pyarrow.parquet as pq
    from pathlib import Path
    import logging
    
    logger = logging.getLogger(__name__)
    daily_file = Path(data_dir) / f"spx_options_{date_str.replace('-', '')}.parquet"
    
    try:
        # Clean the dataframe
        df_clean = day_df.copy()
        
        # Convert all datetime-like columns to strings
        for col in df_clean.columns:
            if 'datetime' in col or 'time' in col:
                df_clean[col] = df_clean[col].astype(str)
        
        # Ensure numeric columns are float64
        numeric_cols = ['open', 'high', 'low', 'close', 'bid', 'ask', 'spread', 'bid_size', 'ask_size', 'spx_price']
        for col in numeric_cols:
            if col in df_clean.columns:
                df_clean[col] = pd.to_numeric(df_clean[col], errors='coerce').astype('float64')
        
        # Ensure string columns are string type
        string_cols = ['contract', 'date']
        for col in string_cols:
            if col in df_clean.columns:
                df_clean[col] = df_clean[col].astype('string')
        
        # Create explicit schema
        schema_fields = []
        for col in df_clean.columns:
            if col in numeric_cols:
                schema_fields.append(pa.field(col, pa.float64()))
            else:
                schema_fields.append(pa.field(col, pa.string()))
        
        schema = pa.schema(schema_fields)
        
        # Convert to pyarrow table and save
        table = pa.Table.from_pandas(df_clean, schema=schema, preserve_index=False)
        pq.write_table(table, daily_file, compression='snappy')
        
        file_size_kb = daily_file.stat().st_size / 1024
        logger.info(f"  💾 ✅ Fixed parquet save: {daily_file} ({file_size_kb:.1f} KB)")
        return True
        
    except Exception as e:
        logger.warning(f"  ⚠️  Fixed parquet save failed: {e}")
        
        # Fallback to CSV
        csv_file = daily_file.with_suffix('.csv')
        try:
            day_df.to_csv(csv_file, index=False)
            logger.info(f"  💾 📄 CSV fallback: {csv_file}")
            return True
        except Exception as csv_e:
            logger.error(f"  ❌ CSV fallback failed: {csv_e}")
            return False
'''
    
    with open('fixed_parquet_save.py', 'w') as f:
        f.write(function_code)
    
    logger.info("💾 Created fixed_parquet_save.py with the corrected function")

def main():
    """Main function to test and create the fix."""
    
    print("🔧 Pandas.period Type Extension Fix")
    print("=" * 40)
    
    # Test the fix
    if test_fix():
        print("✅ Fix verified - pandas.period issue resolved!")
        
        # Create the fixed function file
        create_fixed_save_function()
        
        print("\n📋 To apply this fix to your running download:")
        print("1. The fix has been tested and works")
        print("2. Use the safe_parquet_save() function from this script")
        print("3. Or restart your download with the fixed version")
        
        return True
    else:
        print("❌ Fix test failed - manual intervention needed")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
