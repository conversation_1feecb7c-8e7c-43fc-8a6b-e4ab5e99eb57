#!/usr/bin/env python3
"""
Monitor SPX Options Download Progress

This script monitors the progress of the SPX options download by reading the log file.
"""

import time
import re
from pathlib import Path
from datetime import datetime, timed<PERSON>ta

def parse_log_file(log_file: str = "spx_optimized_download.log"):
    """Parse the log file to extract progress information."""
    
    if not Path(log_file).exists():
        print(f"❌ Log file not found: {log_file}")
        return None
    
    try:
        with open(log_file, 'r') as f:
            lines = f.readlines()
        
        # Extract key metrics from the latest progress line
        progress_info = {
            'days_completed': 0,
            'total_days': 494,
            'success_rate': 0.0,
            'total_records': 0,
            'records_per_minute': 0,
            'api_calls': 0,
            'current_date': None,
            'start_time': None,
            'latest_update': None
        }
        
        # Parse lines for progress information
        for line in lines:
            # Look for progress updates
            if "Progress:" in line:
                # Example: 2025-07-31 18:07:03,805 - INFO - 📈 Progress: 5/494 days, 100.0% success, 19,943 records, 4650 rec/min, 5,011 API calls
                match = re.search(r'Progress: (\d+)/(\d+) days, ([\d.]+)% success, ([\d,]+) records, ([\d,]+) rec/min, ([\d,]+) API calls', line)
                if match:
                    progress_info['days_completed'] = int(match.group(1))
                    progress_info['total_days'] = int(match.group(2))
                    progress_info['success_rate'] = float(match.group(3))
                    progress_info['total_records'] = int(match.group(4).replace(',', ''))
                    progress_info['records_per_minute'] = int(match.group(5).replace(',', ''))
                    progress_info['api_calls'] = int(match.group(6).replace(',', ''))
                    
                    # Extract timestamp
                    timestamp_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                    if timestamp_match:
                        progress_info['latest_update'] = timestamp_match.group(1)
            
            # Look for current processing date
            elif "Processing" in line and "SPX:" in line:
                # Example: 2025-07-31 18:07:03,805 - INFO - 📊 Processing 2023-08-18 (SPX: $4,369.71)
                match = re.search(r'Processing (\d{4}-\d{2}-\d{2}) \(SPX: \$[\d,]+\.[\d]+\)', line)
                if match:
                    progress_info['current_date'] = match.group(1)
            
            # Look for start time
            elif "Starting OPTIMIZED 24-month SPX options download" in line:
                timestamp_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                if timestamp_match:
                    progress_info['start_time'] = timestamp_match.group(1)
        
        return progress_info
        
    except Exception as e:
        print(f"❌ Error parsing log file: {e}")
        return None

def calculate_estimates(progress_info):
    """Calculate time estimates based on current progress."""
    
    if not progress_info or progress_info['days_completed'] == 0:
        return None
    
    try:
        # Calculate elapsed time
        if progress_info['start_time'] and progress_info['latest_update']:
            start_dt = datetime.strptime(progress_info['start_time'], '%Y-%m-%d %H:%M:%S')
            latest_dt = datetime.strptime(progress_info['latest_update'], '%Y-%m-%d %H:%M:%S')
            elapsed_minutes = (latest_dt - start_dt).total_seconds() / 60
        else:
            elapsed_minutes = 0
        
        # Calculate rates
        days_completed = progress_info['days_completed']
        total_days = progress_info['total_days']
        remaining_days = total_days - days_completed
        
        if elapsed_minutes > 0 and days_completed > 0:
            minutes_per_day = elapsed_minutes / days_completed
            estimated_remaining_minutes = remaining_days * minutes_per_day
            estimated_completion = datetime.now() + timedelta(minutes=estimated_remaining_minutes)
        else:
            minutes_per_day = 0
            estimated_remaining_minutes = 0
            estimated_completion = None
        
        # Estimate final data size
        if days_completed > 0:
            records_per_day = progress_info['total_records'] / days_completed
            estimated_total_records = records_per_day * total_days
        else:
            estimated_total_records = 0
        
        return {
            'elapsed_minutes': elapsed_minutes,
            'minutes_per_day': minutes_per_day,
            'estimated_remaining_minutes': estimated_remaining_minutes,
            'estimated_completion': estimated_completion,
            'estimated_total_records': estimated_total_records,
            'progress_percentage': (days_completed / total_days) * 100
        }
        
    except Exception as e:
        print(f"❌ Error calculating estimates: {e}")
        return None

def display_progress(progress_info, estimates):
    """Display formatted progress information."""
    
    print("\n" + "=" * 60)
    print("📊 SPX OPTIONS DOWNLOAD PROGRESS MONITOR")
    print("=" * 60)
    
    if not progress_info:
        print("❌ No progress information available")
        return
    
    # Current status
    print(f"📅 Current Date: {progress_info['current_date'] or 'Unknown'}")
    print(f"📈 Progress: {progress_info['days_completed']}/{progress_info['total_days']} days")
    print(f"✅ Success Rate: {progress_info['success_rate']:.1f}%")
    print(f"📊 Total Records: {progress_info['total_records']:,}")
    print(f"⚡ Processing Speed: {progress_info['records_per_minute']:,} records/minute")
    print(f"🔗 API Calls: {progress_info['api_calls']:,}")
    
    if estimates:
        print(f"\n⏱️  TIMING ESTIMATES:")
        print(f"   Elapsed: {estimates['elapsed_minutes']:.1f} minutes")
        print(f"   Progress: {estimates['progress_percentage']:.2f}%")
        print(f"   Rate: {estimates['minutes_per_day']:.1f} minutes/day")
        print(f"   Remaining: {estimates['estimated_remaining_minutes']:.0f} minutes")
        
        if estimates['estimated_completion']:
            print(f"   ETA: {estimates['estimated_completion'].strftime('%Y-%m-%d %H:%M:%S')}")
        
        print(f"\n📊 DATA ESTIMATES:")
        print(f"   Current Records: {progress_info['total_records']:,}")
        print(f"   Estimated Total: {estimates['estimated_total_records']:,.0f}")
        print(f"   Estimated Size: {estimates['estimated_total_records'] * 0.0005:.1f} MB (compressed)")
    
    print(f"\n🕐 Last Update: {progress_info['latest_update'] or 'Unknown'}")
    print("=" * 60)

def main():
    """Main monitoring function."""
    
    print("🔍 SPX Options Download Monitor")
    print("Press Ctrl+C to exit")
    
    try:
        while True:
            # Clear screen (works on most terminals)
            print("\033[2J\033[H", end="")
            
            # Parse progress
            progress_info = parse_log_file()
            estimates = calculate_estimates(progress_info) if progress_info else None
            
            # Display progress
            display_progress(progress_info, estimates)
            
            # Wait before next update
            time.sleep(30)  # Update every 30 seconds
            
    except KeyboardInterrupt:
        print("\n\n👋 Monitor stopped by user")
    except Exception as e:
        print(f"\n❌ Monitor error: {e}")

if __name__ == "__main__":
    main()
