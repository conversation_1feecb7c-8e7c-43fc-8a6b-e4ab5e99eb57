#!/usr/bin/env python3
"""
Simple SPX Options Data Download Script

Direct approach to download SPX options data without complex imports.
"""

import os
import sys
import json
import time
import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from pathlib import Path
import pytz

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleSPXDownloader:
    """Simplified SPX options data downloader."""
    
    def __init__(self):
        # Load API key from .env file
        self.api_key = self._load_api_key()
        self.base_url = "https://api.polygon.io"
        self.rate_limit_delay = 0.1  # 100ms between requests
        self.data_dir = Path("data")
        self.data_dir.mkdir(exist_ok=True)
        
        # Timezone
        self.est_tz = pytz.timezone('US/Eastern')
        
    def _load_api_key(self):
        """Load API key from .env file."""
        try:
            with open('.env', 'r') as f:
                for line in f:
                    if line.startswith('POLYGON_API_KEY='):
                        return line.split('=', 1)[1].strip()
        except FileNotFoundError:
            logger.error("❌ .env file not found!")
            
        logger.error("❌ POLYGON_API_KEY not found in .env file!")
        return None
    
    def download_spx_prices(self, start_date: str, end_date: str) -> pd.DataFrame:
        """Download SPX daily prices."""
        logger.info(f"📈 Downloading SPX prices from {start_date} to {end_date}")
        
        url = f"{self.base_url}/v2/aggs/ticker/I:SPX/range/1/day/{start_date}/{end_date}"
        params = {
            'apikey': self.api_key,
            'sort': 'asc',
            'limit': 50000
        }
        
        try:
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            data = response.json()
            
            if 'results' in data and data['results']:
                df = pd.DataFrame(data['results'])
                df['date'] = pd.to_datetime(df['t'], unit='ms').dt.date
                df = df.rename(columns={
                    'o': 'spx_open',
                    'h': 'spx_high', 
                    'l': 'spx_low',
                    'c': 'spx_close',
                    'v': 'spx_volume'
                })
                
                df = df[['date', 'spx_open', 'spx_high', 'spx_low', 'spx_close']].sort_values('date')
                logger.info(f"✅ Downloaded {len(df)} SPX price records")
                return df
            else:
                logger.warning("⚠️  No SPX data found")
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"❌ Error downloading SPX prices: {e}")
            return pd.DataFrame()
    
    def get_options_chain(self, date: str, spx_price: float) -> list:
        """Get options chain for a specific date."""
        logger.info(f"📊 Getting options chain for {date} (SPX: ${spx_price:,.2f})")
        
        # Calculate strike range
        min_strike = spx_price - 200
        max_strike = spx_price + 200
        
        # Get contracts for next 30 days
        date_obj = datetime.strptime(date, '%Y-%m-%d')
        exp_start = date_obj + timedelta(days=1)
        exp_end = date_obj + timedelta(days=30)
        
        contracts = []
        
        for contract_type in ['call', 'put']:
            url = f"{self.base_url}/v3/reference/options/contracts"
            params = {
                'underlying_ticker': 'SPX',
                'contract_type': contract_type,
                'as_of': date,
                'expiration_date.gte': exp_start.strftime('%Y-%m-%d'),
                'expiration_date.lte': exp_end.strftime('%Y-%m-%d'),
                'strike_price.gte': min_strike,
                'strike_price.lte': max_strike,
                'limit': 1000,
                'apikey': self.api_key
            }
            
            try:
                response = requests.get(url, params=params, timeout=30)
                response.raise_for_status()
                data = response.json()
                
                if 'results' in data and data['results']:
                    for contract in data['results']:
                        if 'ticker' in contract and 'strike_price' in contract:
                            strike = contract['strike_price']
                            if strike % 10 == 0 or strike % 25 == 0:
                                contracts.append(contract['ticker'])
                
                time.sleep(self.rate_limit_delay)
                
            except Exception as e:
                logger.error(f"❌ Error getting {contract_type} contracts: {e}")
        
        logger.info(f"✅ Found {len(contracts)} contracts")
        return contracts
    
    def download_contract_data(self, contract: str, date: str) -> pd.DataFrame:
        """Download 5-minute data for a contract."""
        try:
            date_obj = datetime.strptime(date, '%Y-%m-%d').date()
            
            # Market hours: 9:30 AM to 4:00 PM EST
            market_open = self.est_tz.localize(datetime.combine(date_obj, datetime.min.time().replace(hour=9, minute=30)))
            market_close = self.est_tz.localize(datetime.combine(date_obj, datetime.min.time().replace(hour=16, minute=0)))
            
            open_timestamp = int(market_open.timestamp() * 1_000_000_000)
            close_timestamp = int(market_close.timestamp() * 1_000_000_000)
            
            url = f"{self.base_url}/v3/quotes/{contract}"
            params = {
                'timestamp.gte': open_timestamp,
                'timestamp.lt': close_timestamp,
                'order': 'asc',
                'limit': 5000,
                'sort': 'timestamp',
                'apikey': self.api_key
            }
            
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            data = response.json()
            
            if 'results' in data and data['results']:
                df = pd.DataFrame(data['results'])
                df['datetime_utc'] = pd.to_datetime(df['sip_timestamp'], unit='ns', utc=True)
                df['datetime'] = df['datetime_utc'].dt.tz_convert(self.est_tz)
                
                # Calculate mid price
                df['price'] = (df['bid_price'] + df['ask_price']) / 2
                df['spread'] = df['ask_price'] - df['bid_price']
                
                # Resample to 5-minute bars
                df = df.set_index('datetime')
                df_5min = df.groupby(pd.Grouper(freq='5min')).agg({
                    'price': ['first', 'max', 'min', 'last'],
                    'bid_price': 'last',
                    'ask_price': 'last',
                    'spread': 'mean'
                }).dropna()
                
                df_5min.columns = ['open', 'high', 'low', 'close', 'bid', 'ask', 'spread']
                df_5min = df_5min.reset_index()
                df_5min['contract'] = contract
                df_5min['date'] = date
                
                time.sleep(self.rate_limit_delay)
                return df_5min
                
        except Exception as e:
            logger.debug(f"No data for {contract} on {date}: {e}")
        
        return pd.DataFrame()
    
    def download_24_months(self):
        """Download 24 months of SPX options data."""
        logger.info("🚀 Starting 24-month SPX options download")
        
        if not self.api_key:
            logger.error("❌ API key not configured")
            return False
        
        # Calculate date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=24 * 30)  # Approximately 24 months
        
        start_str = start_date.strftime('%Y-%m-%d')
        end_str = end_date.strftime('%Y-%m-%d')
        
        logger.info(f"📅 Date range: {start_str} to {end_str}")
        
        # Download SPX prices
        spx_df = self.download_spx_prices(start_str, end_str)
        if spx_df.empty:
            logger.error("❌ Failed to download SPX prices")
            return False
        
        # Download options data for each day
        all_data = []
        total_days = len(spx_df)
        
        for i, (_, row) in enumerate(spx_df.iterrows()):
            date_str = row['date'].strftime('%Y-%m-%d')
            spx_price = row['spx_close']
            
            logger.info(f"📊 [{i+1}/{total_days}] Processing {date_str} (SPX: ${spx_price:,.2f})")
            
            # Get options chain
            contracts = self.get_options_chain(date_str, spx_price)
            
            if not contracts:
                logger.warning(f"⚠️  No contracts for {date_str}")
                continue
            
            # Download data for first 50 contracts (to manage API limits)
            sample_contracts = contracts[:50]
            day_data = []
            
            for j, contract in enumerate(sample_contracts):
                if j % 10 == 0:
                    logger.info(f"  Processing contract {j+1}/{len(sample_contracts)}")
                
                contract_data = self.download_contract_data(contract, date_str)
                if not contract_data.empty:
                    contract_data['spx_price'] = spx_price
                    day_data.append(contract_data)
            
            if day_data:
                day_df = pd.concat(day_data, ignore_index=True)
                all_data.append(day_df)
                logger.info(f"  ✅ {len(day_df)} records from {len(day_data)} contracts")
            
            # Progress update every 10 days
            if (i + 1) % 10 == 0:
                logger.info(f"📈 Progress: {i+1}/{total_days} days completed")
        
        # Save results
        if all_data:
            final_df = pd.concat(all_data, ignore_index=True)
            
            # Save to parquet
            output_file = self.data_dir / "spx_options_24months.parquet"
            try:
                final_df.to_parquet(output_file, index=False)
                logger.info(f"✅ Saved to {output_file}")
            except:
                # Fallback to CSV
                csv_file = self.data_dir / "spx_options_24months.csv"
                final_df.to_csv(csv_file, index=False)
                logger.info(f"✅ Saved to {csv_file}")
            
            # Summary
            logger.info("🎉 Download completed!")
            logger.info(f"📊 Total records: {len(final_df):,}")
            logger.info(f"📅 Date range: {final_df['date'].min()} to {final_df['date'].max()}")
            logger.info(f"🎯 Unique contracts: {final_df['contract'].nunique():,}")
            
            return True
        else:
            logger.error("❌ No data downloaded")
            return False

def main():
    """Main function."""
    print("🚀 Simple SPX Options Downloader - 24 Months")
    print("=" * 50)
    
    downloader = SimpleSPXDownloader()
    
    if not downloader.api_key:
        print("❌ Please configure your POLYGON_API_KEY in the .env file")
        return False
    
    print(f"✅ API Key configured: ...{downloader.api_key[-4:]}")
    print("⚠️  This will download 24 months of SPX options data")
    print("⏰ This may take several hours due to API rate limits")
    
    response = input("\nProceed with download? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("❌ Download cancelled")
        return False
    
    success = downloader.download_24_months()
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
