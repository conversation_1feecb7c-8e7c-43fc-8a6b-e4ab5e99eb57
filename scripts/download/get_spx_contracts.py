#!/usr/bin/env python3
"""
Get SPX Options Contracts within +/- 200 points from current price

This script fetches current SPX price and retrieves options contracts
within a 200-point range above and below the current price.
"""

import os
import requests
import pandas as pd
from datetime import datetime, timedelta
import logging
import json

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SPXContractsFetcher:
    """Fetch SPX options contracts within price range."""
    
    def __init__(self):
        self.api_key = self._load_api_key()
        self.base_url = "https://api.polygon.io"
        
    def _load_api_key(self):
        """Load API key from .env file."""
        try:
            with open('.env', 'r') as f:
                for line in f:
                    if line.startswith('POLYGON_API_KEY='):
                        return line.split('=', 1)[1].strip()
        except FileNotFoundError:
            logger.error("❌ .env file not found!")
            
        logger.error("❌ POLYGON_API_KEY not found in .env file!")
        return None
    
    def get_current_spx_price(self):
        """Get the current SPX price."""
        logger.info("📈 Fetching current SPX price...")
        
        # Get the most recent trading day
        today = datetime.now()
        # Go back a few days to ensure we get a trading day
        for i in range(5):
            check_date = (today - timedelta(days=i)).strftime('%Y-%m-%d')
            
            url = f"{self.base_url}/v2/aggs/ticker/I:SPX/range/1/day/{check_date}/{check_date}"
            params = {
                'apikey': self.api_key,
                'sort': 'desc',
                'limit': 1
            }
            
            try:
                response = requests.get(url, params=params, timeout=30)
                response.raise_for_status()
                data = response.json()
                
                if 'results' in data and data['results']:
                    price = data['results'][0]['c']  # closing price
                    logger.info(f"✅ Current SPX price: ${price:,.2f} (as of {check_date})")
                    return price, check_date
                    
            except Exception as e:
                logger.debug(f"No data for {check_date}: {e}")
                continue
        
        logger.error("❌ Could not fetch current SPX price")
        return None, None
    
    def get_contracts_in_range(self, spx_price, price_range=200, days_out=30):
        """
        Get options contracts within price range.
        
        Args:
            spx_price: Current SPX price
            price_range: Points above and below SPX price (default: 200)
            days_out: Days out for expiration (default: 30)
        """
        logger.info(f"📊 Getting contracts within +/- {price_range} points of ${spx_price:,.2f}")
        
        min_strike = spx_price - price_range
        max_strike = spx_price + price_range
        
        logger.info(f"Strike range: ${min_strike:,.0f} - ${max_strike:,.0f}")
        
        # Calculate expiration date range
        today = datetime.now()
        exp_start = today + timedelta(days=1)  # Tomorrow
        exp_end = today + timedelta(days=days_out)  # X days out
        
        all_contracts = []
        
        for contract_type in ['call', 'put']:
            logger.info(f"🔍 Fetching {contract_type} contracts...")
            
            url = f"{self.base_url}/v3/reference/options/contracts"
            params = {
                'underlying_ticker': 'SPX',
                'contract_type': contract_type,
                'expiration_date.gte': exp_start.strftime('%Y-%m-%d'),
                'expiration_date.lte': exp_end.strftime('%Y-%m-%d'),
                'strike_price.gte': min_strike,
                'strike_price.lte': max_strike,
                'limit': 1000,
                'apikey': self.api_key
            }
            
            try:
                response = requests.get(url, params=params, timeout=30)
                response.raise_for_status()
                data = response.json()
                
                if 'results' in data and data['results']:
                    contracts = data['results']
                    logger.info(f"✅ Found {len(contracts)} {contract_type} contracts")
                    
                    # Process contracts
                    for contract in contracts:
                        contract_info = {
                            'ticker': contract.get('ticker'),
                            'contract_type': contract.get('contract_type'),
                            'strike_price': contract.get('strike_price'),
                            'expiration_date': contract.get('expiration_date'),
                            'underlying_ticker': contract.get('underlying_ticker'),
                            'distance_from_spot': abs(contract.get('strike_price', 0) - spx_price)
                        }
                        all_contracts.append(contract_info)
                else:
                    logger.warning(f"⚠️  No {contract_type} contracts found")
                    
            except Exception as e:
                logger.error(f"❌ Error fetching {contract_type} contracts: {e}")
        
        return all_contracts
    
    def analyze_contracts(self, contracts, spx_price):
        """Analyze the retrieved contracts."""
        if not contracts:
            logger.warning("⚠️  No contracts to analyze")
            return
        
        df = pd.DataFrame(contracts)
        
        logger.info("📊 Contract Analysis:")
        logger.info(f"  Total contracts: {len(df)}")
        logger.info(f"  Calls: {len(df[df['contract_type'] == 'call'])}")
        logger.info(f"  Puts: {len(df[df['contract_type'] == 'put'])}")
        
        # Strike price analysis
        logger.info(f"  Strike range: ${df['strike_price'].min():,.0f} - ${df['strike_price'].max():,.0f}")
        logger.info(f"  Current SPX: ${spx_price:,.2f}")
        
        # Expiration analysis
        df['expiration_date'] = pd.to_datetime(df['expiration_date'])
        logger.info(f"  Expiration range: {df['expiration_date'].min().date()} to {df['expiration_date'].max().date()}")
        
        # Show contracts closest to current price
        logger.info("\n🎯 Contracts closest to current SPX price:")
        closest = df.nsmallest(10, 'distance_from_spot')[['ticker', 'contract_type', 'strike_price', 'expiration_date', 'distance_from_spot']]
        print(closest.to_string(index=False))
        
        # Show ATM contracts by expiration
        logger.info("\n📅 At-the-money contracts by expiration:")
        atm_range = 25  # Within 25 points of current price
        atm_contracts = df[df['distance_from_spot'] <= atm_range].copy()
        
        if not atm_contracts.empty:
            atm_summary = atm_contracts.groupby(['expiration_date', 'contract_type']).size().unstack(fill_value=0)
            print(atm_summary)
        
        return df
    
    def save_contracts(self, contracts, filename="spx_contracts.csv"):
        """Save contracts to file."""
        if not contracts:
            logger.warning("⚠️  No contracts to save")
            return
        
        df = pd.DataFrame(contracts)
        
        # Save as CSV
        df.to_csv(filename, index=False)
        logger.info(f"💾 Saved {len(df)} contracts to {filename}")
        
        # Also save as JSON for easy reading
        json_filename = filename.replace('.csv', '.json')
        with open(json_filename, 'w') as f:
            json.dump(contracts, f, indent=2, default=str)
        logger.info(f"💾 Saved contracts to {json_filename}")

def main():
    """Main function."""
    print("🎯 SPX Options Contracts Fetcher (+/- 200 points)")
    print("=" * 55)
    
    fetcher = SPXContractsFetcher()
    
    if not fetcher.api_key:
        print("❌ Please configure your POLYGON_API_KEY in the .env file")
        return False
    
    print(f"✅ API Key configured: ...{fetcher.api_key[-4:]}")
    
    # Get current SPX price
    spx_price, price_date = fetcher.get_current_spx_price()
    if not spx_price:
        print("❌ Could not fetch SPX price")
        return False
    
    # Get user preferences
    print(f"\nCurrent SPX Price: ${spx_price:,.2f} (as of {price_date})")
    
    try:
        price_range = input(f"Price range (+/- points) [default: 200]: ").strip()
        price_range = int(price_range) if price_range else 200
        
        days_out = input(f"Days to expiration [default: 30]: ").strip()
        days_out = int(days_out) if days_out else 30
        
    except ValueError:
        print("❌ Invalid input, using defaults")
        price_range = 200
        days_out = 30
    
    print(f"\n🔍 Searching for contracts:")
    print(f"  Strike range: ${spx_price - price_range:,.0f} - ${spx_price + price_range:,.0f}")
    print(f"  Expiration: Next {days_out} days")
    
    # Fetch contracts
    contracts = fetcher.get_contracts_in_range(spx_price, price_range, days_out)
    
    if contracts:
        # Analyze contracts
        df = fetcher.analyze_contracts(contracts, spx_price)
        
        # Save contracts
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"spx_contracts_{timestamp}.csv"
        fetcher.save_contracts(contracts, filename)
        
        print(f"\n🎉 Success! Found {len(contracts)} contracts")
        print(f"📁 Data saved to: {filename}")
        
        return True
    else:
        print("❌ No contracts found")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
