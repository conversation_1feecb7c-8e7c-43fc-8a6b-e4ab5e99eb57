#!/usr/bin/env python3
"""
Download SPX Options Data for 24 Months

This script downloads 24 months of SPX options data using the integrated data downloader.
"""

import os
import sys
import logging
from datetime import datetime
from pathlib import Path

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('spx_download.log')
    ]
)
logger = logging.getLogger(__name__)

def download_spx_options_24_months():
    """Download 24 months of SPX options data."""
    
    logger.info("🚀 Starting SPX Options Data Download (24 months)")
    logger.info("=" * 60)
    
    try:
        # Import the SPX data downloader
        from quantlab.data.data_downloader import SPXDataDownloader
        logger.info("✅ SPX Data Downloader imported successfully")
        
        # Initialize the downloader
        downloader = SPXDataDownloader()
        logger.info("✅ SPX Data Downloader initialized")
        
        # Check current configuration
        logger.info("📊 Current Configuration:")
        logger.info(f"  API Key: {'*' * 20}...{downloader.api_key[-4:] if len(downloader.api_key) > 4 else 'Not Set'}")
        logger.info(f"  Data Directory: {downloader.file_paths['data_dir']}")
        logger.info(f"  Master File: {downloader.file_paths['master_file']}")
        
        # Check for existing data
        metadata = downloader.get_download_metadata()
        if metadata:
            logger.info("📋 Existing Download Metadata:")
            for key, value in metadata.items():
                logger.info(f"  {key}: {value}")
        else:
            logger.info("📋 No existing download metadata found")
        
        # Start the download
        logger.info("🔄 Starting full download for 24 months...")
        logger.info("⏰ This may take a while depending on your API rate limits...")
        
        success = downloader.full_download(
            months=24,
            calculate_greeks=True
        )
        
        if success:
            logger.info("🎉 Download completed successfully!")
            
            # Check the results
            master_file = downloader.file_paths['master_file']
            if os.path.exists(master_file):
                try:
                    import pandas as pd
                    df = pd.read_parquet(master_file)
                    
                    logger.info("📊 Download Results:")
                    logger.info(f"  Total Records: {len(df):,}")
                    logger.info(f"  Date Range: {df['date'].min()} to {df['date'].max()}")
                    logger.info(f"  Unique Contracts: {df['contract'].nunique():,}")
                    logger.info(f"  Trading Days: {df['date'].nunique()}")
                    
                    # Show column information
                    logger.info(f"  Columns: {list(df.columns)}")
                    
                    # Show sample data
                    logger.info("📋 Sample Data (first 3 rows):")
                    print(df.head(3).to_string())
                    
                    # File size information
                    file_size_mb = os.path.getsize(master_file) / (1024 * 1024)
                    logger.info(f"  File Size: {file_size_mb:.2f} MB")
                    
                except Exception as e:
                    logger.error(f"❌ Error reading downloaded data: {e}")
            else:
                logger.error(f"❌ Master file not found: {master_file}")
        else:
            logger.error("❌ Download failed!")
            return False
            
    except ImportError as e:
        logger.error(f"❌ Import error: {e}")
        logger.error("Make sure all dependencies are installed and the data downloader is properly configured")
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return success

def check_prerequisites():
    """Check if all prerequisites are met."""
    logger.info("🔍 Checking prerequisites...")
    
    # Check if .env file exists
    if not os.path.exists('.env'):
        logger.error("❌ .env file not found!")
        logger.error("Please create a .env file with your POLYGON_API_KEY")
        return False
    
    # Check if API key is set
    try:
        from quantlab.config.config import get_api_key
        api_key = get_api_key()
        if not api_key or api_key == "your_api_key_here":
            logger.error("❌ POLYGON_API_KEY not properly configured!")
            logger.error("Please set your Polygon.io API key in the .env file")
            return False
        logger.info("✅ API key configured")
    except Exception as e:
        logger.error(f"❌ Error checking API key: {e}")
        return False
    
    # Check required directories
    data_dir = Path("data")
    data_dir.mkdir(exist_ok=True)
    logger.info("✅ Data directory ready")
    
    # Check dependencies
    try:
        import pandas
        import pyarrow
        import requests
        import pytz
        logger.info("✅ Required dependencies available")
    except ImportError as e:
        logger.error(f"❌ Missing dependency: {e}")
        logger.error("Please run: pip install -r requirements.txt")
        return False
    
    return True

def main():
    """Main function."""
    logger.info("SPX Options Data Downloader - 24 Months")
    logger.info("=" * 50)
    
    # Check prerequisites
    if not check_prerequisites():
        logger.error("❌ Prerequisites not met. Please fix the issues above.")
        return False
    
    # Confirm with user
    print("\n" + "=" * 60)
    print("📊 SPX OPTIONS DATA DOWNLOAD - 24 MONTHS")
    print("=" * 60)
    print("This will download 24 months of SPX options data including:")
    print("  • Historical options chains")
    print("  • 5-minute intraday bars")
    print("  • Greeks calculations")
    print("  • Bid/ask spreads")
    print("\n⚠️  WARNING:")
    print("  • This may take several hours to complete")
    print("  • Large amount of data will be downloaded")
    print("  • API rate limits will be respected")
    print("  • Ensure stable internet connection")
    
    response = input("\nDo you want to proceed? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("❌ Download cancelled by user")
        return False
    
    # Start download
    success = download_spx_options_24_months()
    
    if success:
        print("\n🎉 SUCCESS!")
        print("SPX options data has been downloaded successfully.")
        print("Check the 'data' directory for the downloaded files.")
        print("Logs have been saved to 'spx_download.log'")
    else:
        print("\n❌ FAILED!")
        print("Download failed. Check the logs above for details.")
        print("Common issues:")
        print("  • Invalid API key")
        print("  • Network connectivity problems")
        print("  • API rate limit exceeded")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
