# Download Scripts

Scripts for downloading SPX options data from Polygon.io API.

## Main Scripts

- `download_spx_optimized.py` - **Recommended**: Optimized downloader with parquet support
- `get_spx_contracts.py` - Fetch contracts within price range
- `monitor_download.py` - Monitor download progress

## Utility Scripts

- `restart_with_fix.py` - Restart download with pandas.period fix
- `simple_spx_download.py` - Simple downloader for testing

## Usage

```bash
# Download 24 months of SPX options data
python download_spx_optimized.py

# Get contracts within +/- 200 points
python get_spx_contracts.py

# Monitor active download
python monitor_download.py
```
