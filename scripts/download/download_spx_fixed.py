#!/usr/bin/env python3
"""
Fixed SPX Options Data Download - 24 Months with Parquet Support

This version fixes all pandas/pyarrow compatibility issues by:
- Using string datetime representations
- Avoiding problematic pandas period types
- Proper data type handling for parquet
"""

import os
import sys
import json
import time
import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from pathlib import Path
import pytz
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('spx_fixed_download.log')
    ]
)
logger = logging.getLogger(__name__)

class FixedSPXDownloader:
    """Fixed SPX options data downloader with proper parquet support."""
    
    def __init__(self):
        self.api_key = self._load_api_key()
        self.base_url = "https://api.polygon.io"
        self.rate_limit_delay = 0.11
        self.max_workers = 8
        self.data_dir = Path("data")
        self.data_dir.mkdir(exist_ok=True)
        
        # Timezone
        self.est_tz = pytz.timezone('US/Eastern')
        
        # Threading and progress tracking
        self.download_lock = Lock()
        self.api_calls_made = 0
        self.total_records = 0
        
    def _load_api_key(self):
        """Load API key from .env file."""
        try:
            with open('.env', 'r') as f:
                for line in f:
                    if line.startswith('POLYGON_API_KEY='):
                        return line.split('=', 1)[1].strip()
        except FileNotFoundError:
            logger.error("❌ .env file not found!")
        return None
    
    def download_spx_prices(self, start_date: str, end_date: str) -> pd.DataFrame:
        """Download SPX daily prices."""
        logger.info(f"📈 Downloading SPX prices from {start_date} to {end_date}")
        
        url = f"{self.base_url}/v2/aggs/ticker/I:SPX/range/1/day/{start_date}/{end_date}"
        params = {'apikey': self.api_key, 'sort': 'asc', 'limit': 50000}
        
        try:
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            data = response.json()
            
            if 'results' in data and data['results']:
                df = pd.DataFrame(data['results'])
                df['date'] = pd.to_datetime(df['t'], unit='ms').dt.strftime('%Y-%m-%d')  # String format
                df = df.rename(columns={'o': 'spx_open', 'h': 'spx_high', 'l': 'spx_low', 'c': 'spx_close'})
                df = df[['date', 'spx_open', 'spx_high', 'spx_low', 'spx_close']].sort_values('date')
                
                logger.info(f"✅ Downloaded {len(df)} SPX price records")
                self.api_calls_made += 1
                return df
            else:
                logger.warning("⚠️  No SPX data found")
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"❌ Error downloading SPX prices: {e}")
            return pd.DataFrame()
    
    def get_prioritized_contracts(self, date: str, spx_price: float, price_range: int = 200, max_contracts: int = 1000) -> list:
        """Get options contracts prioritized by distance from ATM."""
        logger.debug(f"📊 Getting contracts for {date} (SPX: ${spx_price:,.2f})")
        
        min_strike = spx_price - price_range
        max_strike = spx_price + price_range
        
        date_obj = datetime.strptime(date, '%Y-%m-%d')
        exp_start = date_obj + timedelta(days=1)
        exp_end = date_obj + timedelta(days=30)
        
        all_contracts = []
        
        for contract_type in ['call', 'put']:
            url = f"{self.base_url}/v3/reference/options/contracts"
            params = {
                'underlying_ticker': 'SPX',
                'contract_type': contract_type,
                'as_of': date,
                'expiration_date.gte': exp_start.strftime('%Y-%m-%d'),
                'expiration_date.lte': exp_end.strftime('%Y-%m-%d'),
                'strike_price.gte': min_strike,
                'strike_price.lte': max_strike,
                'limit': 1000,
                'apikey': self.api_key
            }
            
            try:
                response = requests.get(url, params=params, timeout=30)
                response.raise_for_status()
                data = response.json()
                
                if 'results' in data and data['results']:
                    for contract in data['results']:
                        if 'ticker' in contract and 'strike_price' in contract:
                            strike = contract['strike_price']
                            if strike % 5 == 0:
                                distance = abs(strike - spx_price)
                                all_contracts.append((contract['ticker'], distance, strike))
                
                time.sleep(self.rate_limit_delay)
                self.api_calls_made += 1
                
            except Exception as e:
                logger.debug(f"Error getting {contract_type} contracts: {e}")
        
        # Sort by distance from ATM and take closest contracts
        all_contracts.sort(key=lambda x: x[1])
        prioritized_contracts = [c[0] for c in all_contracts[:max_contracts]]
        
        logger.info(f"  📊 Prioritized {len(prioritized_contracts)} contracts (closest to ATM)")
        return prioritized_contracts
    
    def download_contract_data(self, contract: str, date: str) -> pd.DataFrame:
        """Download 5-minute data for a contract with fixed data types."""
        try:
            date_obj = datetime.strptime(date, '%Y-%m-%d').date()
            market_open = self.est_tz.localize(datetime.combine(date_obj, datetime.min.time().replace(hour=9, minute=30)))
            market_close = self.est_tz.localize(datetime.combine(date_obj, datetime.min.time().replace(hour=16, minute=0)))
            
            open_timestamp = int(market_open.timestamp() * 1_000_000_000)
            close_timestamp = int(market_close.timestamp() * 1_000_000_000)
            
            url = f"{self.base_url}/v3/quotes/{contract}"
            params = {
                'timestamp.gte': open_timestamp,
                'timestamp.lt': close_timestamp,
                'order': 'asc',
                'limit': 5000,
                'sort': 'timestamp',
                'apikey': self.api_key
            }
            
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            data = response.json()
            
            if 'results' in data and data['results']:
                df = pd.DataFrame(data['results'])
                df['datetime_utc'] = pd.to_datetime(df['sip_timestamp'], unit='ns', utc=True)
                df['datetime_est'] = df['datetime_utc'].dt.tz_convert(self.est_tz)
                
                df['price'] = (df['bid_price'] + df['ask_price']) / 2
                df['spread'] = df['ask_price'] - df['bid_price']
                
                # Resample to 5-minute bars
                df = df.set_index('datetime_est')
                df_5min = df.groupby(pd.Grouper(freq='5min')).agg({
                    'price': ['first', 'max', 'min', 'last'],
                    'bid_price': 'last',
                    'ask_price': 'last',
                    'spread': 'mean',
                    'bid_size': 'last',
                    'ask_size': 'last'
                }).dropna()
                
                if not df_5min.empty:
                    df_5min.columns = ['open', 'high', 'low', 'close', 'bid', 'ask', 'spread', 'bid_size', 'ask_size']
                    df_5min = df_5min.reset_index()
                    
                    # Convert datetime to string format to avoid pandas/pyarrow issues
                    df_5min['datetime'] = df_5min['datetime_est'].dt.strftime('%Y-%m-%d %H:%M:%S%z')
                    df_5min['time_est'] = df_5min['datetime_est'].dt.strftime('%H:%M:%S')
                    df_5min['date'] = date
                    df_5min['contract'] = contract
                    
                    # Drop the original datetime column
                    df_5min = df_5min.drop('datetime_est', axis=1)
                    
                    # Ensure all numeric columns are proper float64
                    numeric_cols = ['open', 'high', 'low', 'close', 'bid', 'ask', 'spread', 'bid_size', 'ask_size']
                    for col in numeric_cols:
                        df_5min[col] = pd.to_numeric(df_5min[col], errors='coerce').astype('float64')
                    
                    time.sleep(self.rate_limit_delay)
                    self.api_calls_made += 1
                    return df_5min
                
        except Exception as e:
            logger.debug(f"No data for {contract} on {date}: {e}")
        
        return pd.DataFrame()
    
    def save_daily_parquet(self, day_df: pd.DataFrame, date_str: str) -> bool:
        """Save daily data as parquet with proper data type handling."""
        daily_file = self.data_dir / f"spx_options_{date_str.replace('-', '')}.parquet"
        
        try:
            # Ensure consistent data types
            day_df_clean = day_df.copy()
            
            # Ensure string columns are strings
            string_cols = ['datetime', 'time_est', 'date', 'contract']
            for col in string_cols:
                if col in day_df_clean.columns:
                    day_df_clean[col] = day_df_clean[col].astype('string')
            
            # Ensure numeric columns are float64
            numeric_cols = ['open', 'high', 'low', 'close', 'bid', 'ask', 'spread', 'bid_size', 'ask_size', 'spx_price']
            for col in numeric_cols:
                if col in day_df_clean.columns:
                    day_df_clean[col] = pd.to_numeric(day_df_clean[col], errors='coerce').astype('float64')
            
            # Save with explicit data types
            day_df_clean.to_parquet(daily_file, index=False, engine='pyarrow', compression='snappy')
            
            file_size_kb = daily_file.stat().st_size / 1024
            logger.info(f"  💾 ✅ Saved daily parquet: {daily_file} ({file_size_kb:.1f} KB)")
            return True
            
        except Exception as e:
            logger.warning(f"  ⚠️  Parquet save failed: {e}")
            
            # Fallback to CSV
            csv_file = daily_file.with_suffix('.csv')
            try:
                day_df.to_csv(csv_file, index=False)
                logger.info(f"  💾 📄 Saved daily CSV: {csv_file}")
                return True
            except Exception as csv_e:
                logger.error(f"  ❌ CSV save also failed: {csv_e}")
                return False
    
    def download_day_data(self, date_str: str, spx_price: float, max_contracts: int = 1000) -> pd.DataFrame:
        """Download options data for a single day."""
        logger.info(f"📊 Processing {date_str} (SPX: ${spx_price:,.2f})")
        
        # Get prioritized contracts
        contracts = self.get_prioritized_contracts(date_str, spx_price, max_contracts=max_contracts)
        
        if not contracts:
            logger.warning(f"⚠️  No contracts found for {date_str}")
            return pd.DataFrame()
        
        # Download data with threading
        day_data = []
        successful_downloads = 0
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_contract = {
                executor.submit(self.download_contract_data, contract, date_str): contract 
                for contract in contracts
            }
            
            for future in as_completed(future_to_contract):
                contract = future_to_contract[future]
                try:
                    contract_data = future.result()
                    if not contract_data.empty:
                        contract_data['spx_price'] = spx_price
                        with self.download_lock:
                            day_data.append(contract_data)
                            self.total_records += len(contract_data)
                        successful_downloads += 1
                except Exception as e:
                    logger.debug(f"Error downloading {contract}: {e}")
        
        if day_data:
            day_df = pd.concat(day_data, ignore_index=True)
            logger.info(f"  ✅ {len(day_df)} records from {successful_downloads}/{len(contracts)} contracts")
            
            # Save daily backup with fixed parquet handling
            self.save_daily_parquet(day_df, date_str)
            
            return day_df
        else:
            logger.warning(f"  ⚠️  No data retrieved for {date_str}")
            return pd.DataFrame()

def main():
    """Main function."""
    print("🔧 Fixed SPX Options 24-Month Download")
    print("=" * 45)
    print("✨ Fixes:")
    print("  • Pandas/PyArrow compatibility issues resolved")
    print("  • Proper parquet saving with data type handling")
    print("  • String datetime representations")
    print("  • Enhanced error handling")
    
    downloader = FixedSPXDownloader()
    
    if not downloader.api_key:
        print("❌ Please configure your POLYGON_API_KEY in the .env file")
        return False
    
    print(f"\n✅ API Key configured: ...{downloader.api_key[-4:]}")
    
    response = input("\nStart fixed 24-month download? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("❌ Download cancelled")
        return False
    
    # For testing, let's just do a few days first
    print("🧪 Testing with first 3 days to verify parquet saving works...")
    
    # Test with recent dates
    end_date = datetime.now()
    start_date = end_date - timedelta(days=5)
    
    start_str = start_date.strftime('%Y-%m-%d')
    end_str = end_date.strftime('%Y-%m-%d')
    
    logger.info(f"🧪 Test date range: {start_str} to {end_str}")
    
    # Download SPX prices
    spx_df = downloader.download_spx_prices(start_str, end_str)
    if spx_df.empty:
        logger.error("❌ Failed to download SPX prices")
        return False
    
    # Test with first 3 days
    test_days = min(3, len(spx_df))
    logger.info(f"🧪 Testing with {test_days} days")
    
    for i, (_, row) in enumerate(spx_df.head(test_days).iterrows()):
        date_str = row['date']
        spx_price = row['spx_close']
        
        day_data = downloader.download_day_data(date_str, spx_price, max_contracts=100)  # Smaller test
        
        if not day_data.empty:
            logger.info(f"✅ Test day {i+1} successful: {len(day_data)} records")
        else:
            logger.warning(f"⚠️  Test day {i+1} failed")
    
    print(f"\n🎉 Test completed! Check the data/ directory for parquet files.")
    print(f"📁 If parquet files were created successfully, you can run the full 24-month download.")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
