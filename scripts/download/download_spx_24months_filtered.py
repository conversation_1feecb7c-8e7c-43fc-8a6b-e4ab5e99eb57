#!/usr/bin/env python3
"""
Download 24 Months of SPX Options Data (+/- 200 from price)

This script downloads 24 months of SPX options data for contracts within
+/- 200 points from the SPX price on each trading day.
"""

import os
import sys
import json
import time
import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from pathlib import Path
import pytz
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('spx_24month_download.log')
    ]
)
logger = logging.getLogger(__name__)

class SPX24MonthDownloader:
    """Download 24 months of SPX options data with price filtering."""
    
    def __init__(self):
        self.api_key = self._load_api_key()
        self.base_url = "https://api.polygon.io"
        self.rate_limit_delay = 0.12  # 120ms between requests (safe for free tier)
        self.max_workers = 5  # Conservative threading
        self.data_dir = Path("data")
        self.data_dir.mkdir(exist_ok=True)
        
        # Timezone
        self.est_tz = pytz.timezone('US/Eastern')
        
        # Threading
        self.download_lock = Lock()
        self.api_calls_made = 0
        
    def _load_api_key(self):
        """Load API key from .env file."""
        try:
            with open('.env', 'r') as f:
                for line in f:
                    if line.startswith('POLYGON_API_KEY='):
                        return line.split('=', 1)[1].strip()
        except FileNotFoundError:
            logger.error("❌ .env file not found!")
            
        logger.error("❌ POLYGON_API_KEY not found in .env file!")
        return None
    
    def download_spx_prices(self, start_date: str, end_date: str) -> pd.DataFrame:
        """Download SPX daily prices for the date range."""
        logger.info(f"📈 Downloading SPX prices from {start_date} to {end_date}")
        
        url = f"{self.base_url}/v2/aggs/ticker/I:SPX/range/1/day/{start_date}/{end_date}"
        params = {
            'apikey': self.api_key,
            'sort': 'asc',
            'limit': 50000
        }
        
        try:
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            data = response.json()
            
            if 'results' in data and data['results']:
                df = pd.DataFrame(data['results'])
                df['date'] = pd.to_datetime(df['t'], unit='ms').dt.date
                df = df.rename(columns={
                    'o': 'spx_open',
                    'h': 'spx_high', 
                    'l': 'spx_low',
                    'c': 'spx_close',
                    'v': 'spx_volume'
                })
                
                df = df[['date', 'spx_open', 'spx_high', 'spx_low', 'spx_close']].sort_values('date')
                logger.info(f"✅ Downloaded {len(df)} SPX price records")
                self.api_calls_made += 1
                return df
            else:
                logger.warning("⚠️  No SPX data found")
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"❌ Error downloading SPX prices: {e}")
            return pd.DataFrame()
    
    def get_options_chain_for_date(self, date: str, spx_price: float, price_range: int = 200) -> list:
        """Get options chain for a specific date within price range."""
        logger.debug(f"📊 Getting options chain for {date} (SPX: ${spx_price:,.2f})")
        
        # Calculate strike range
        min_strike = spx_price - price_range
        max_strike = spx_price + price_range
        
        # Get contracts for next 30 days from this date
        date_obj = datetime.strptime(date, '%Y-%m-%d')
        exp_start = date_obj + timedelta(days=1)
        exp_end = date_obj + timedelta(days=30)
        
        contracts = []
        
        for contract_type in ['call', 'put']:
            url = f"{self.base_url}/v3/reference/options/contracts"
            params = {
                'underlying_ticker': 'SPX',
                'contract_type': contract_type,
                'as_of': date,  # Historical contracts as they existed on this date
                'expiration_date.gte': exp_start.strftime('%Y-%m-%d'),
                'expiration_date.lte': exp_end.strftime('%Y-%m-%d'),
                'strike_price.gte': min_strike,
                'strike_price.lte': max_strike,
                'limit': 1000,
                'apikey': self.api_key
            }
            
            try:
                response = requests.get(url, params=params, timeout=30)
                response.raise_for_status()
                data = response.json()
                
                if 'results' in data and data['results']:
                    for contract in data['results']:
                        if 'ticker' in contract and 'strike_price' in contract:
                            strike = contract['strike_price']
                            # Filter for standard strikes (multiples of 5, 10, or 25)
                            if strike % 5 == 0:
                                contracts.append(contract['ticker'])
                
                time.sleep(self.rate_limit_delay)
                self.api_calls_made += 1
                
            except Exception as e:
                logger.debug(f"Error getting {contract_type} contracts for {date}: {e}")
        
        logger.debug(f"Found {len(contracts)} contracts for {date}")
        return contracts
    
    def download_contract_quotes(self, contract: str, date: str) -> pd.DataFrame:
        """Download 5-minute quote data for a contract on a specific date."""
        try:
            date_obj = datetime.strptime(date, '%Y-%m-%d').date()
            
            # Market hours: 9:30 AM to 4:00 PM EST
            market_open = self.est_tz.localize(datetime.combine(date_obj, datetime.min.time().replace(hour=9, minute=30)))
            market_close = self.est_tz.localize(datetime.combine(date_obj, datetime.min.time().replace(hour=16, minute=0)))
            
            open_timestamp = int(market_open.timestamp() * 1_000_000_000)
            close_timestamp = int(market_close.timestamp() * 1_000_000_000)
            
            url = f"{self.base_url}/v3/quotes/{contract}"
            params = {
                'timestamp.gte': open_timestamp,
                'timestamp.lt': close_timestamp,
                'order': 'asc',
                'limit': 5000,
                'sort': 'timestamp',
                'apikey': self.api_key
            }
            
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            data = response.json()
            
            if 'results' in data and data['results']:
                df = pd.DataFrame(data['results'])
                df['datetime_utc'] = pd.to_datetime(df['sip_timestamp'], unit='ns', utc=True)
                df['datetime'] = df['datetime_utc'].dt.tz_convert(self.est_tz)
                
                # Calculate mid price and spread
                df['price'] = (df['bid_price'] + df['ask_price']) / 2
                df['spread'] = df['ask_price'] - df['bid_price']
                
                # Resample to 5-minute bars
                df = df.set_index('datetime')
                df_5min = df.groupby(pd.Grouper(freq='5min')).agg({
                    'price': ['first', 'max', 'min', 'last'],
                    'bid_price': 'last',
                    'ask_price': 'last',
                    'spread': 'mean',
                    'bid_size': 'last',
                    'ask_size': 'last'
                }).dropna()
                
                if not df_5min.empty:
                    df_5min.columns = ['open', 'high', 'low', 'close', 'bid', 'ask', 'spread', 'bid_size', 'ask_size']
                    df_5min = df_5min.reset_index()
                    df_5min['contract'] = contract
                    df_5min['date'] = date
                    df_5min['time_est'] = df_5min['datetime'].dt.time
                    
                    time.sleep(self.rate_limit_delay)
                    self.api_calls_made += 1
                    return df_5min
                
        except Exception as e:
            logger.debug(f"No data for {contract} on {date}: {e}")
        
        return pd.DataFrame()
    
    def download_day_data(self, date_str: str, spx_price: float, max_contracts: int = 1000) -> pd.DataFrame:
        """Download options data for a single day."""
        logger.info(f"📊 Processing {date_str} (SPX: ${spx_price:,.2f})")
        
        # Get options chain for this date
        contracts = self.get_options_chain_for_date(date_str, spx_price)
        
        if not contracts:
            logger.warning(f"⚠️  No contracts found for {date_str}")
            return pd.DataFrame()
        
        # Limit contracts if specified (but allow more for better data coverage)
        if len(contracts) > max_contracts:
            # Prioritize contracts closer to ATM by sorting by distance from current price
            # Parse contract strikes and sort by distance from SPX price
            contract_distances = []
            for contract in contracts:
                try:
                    # Extract strike from contract ticker (e.g., O:SPX250815C06340000 -> 6340)
                    if 'C' in contract or 'P' in contract:
                        strike_part = contract.split('C')[-1] if 'C' in contract else contract.split('P')[-1]
                        strike = float(strike_part) / 1000  # Convert from format like 06340000 to 6340
                        distance = abs(strike - spx_price)
                        contract_distances.append((contract, distance))
                except:
                    # If parsing fails, assign large distance
                    contract_distances.append((contract, 999999))

            # Sort by distance and take closest contracts
            contract_distances.sort(key=lambda x: x[1])
            contracts = [c[0] for c in contract_distances[:max_contracts]]
            logger.info(f"  Prioritized {max_contracts} contracts closest to ATM (SPX: ${spx_price:,.2f})")
        
        # Download data for each contract
        day_data = []
        successful_downloads = 0
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_contract = {
                executor.submit(self.download_contract_quotes, contract, date_str): contract 
                for contract in contracts
            }
            
            for future in as_completed(future_to_contract):
                contract = future_to_contract[future]
                try:
                    contract_data = future.result()
                    if not contract_data.empty:
                        contract_data['spx_price'] = spx_price
                        with self.download_lock:
                            day_data.append(contract_data)
                        successful_downloads += 1
                except Exception as e:
                    logger.debug(f"Error downloading {contract}: {e}")
        
        if day_data:
            day_df = pd.concat(day_data, ignore_index=True)
            logger.info(f"  ✅ {len(day_df)} records from {successful_downloads}/{len(contracts)} contracts")
            return day_df
        else:
            logger.warning(f"  ⚠️  No data retrieved for {date_str}")
            return pd.DataFrame()
    
    def download_24_months(self, price_range: int = 200, max_contracts_per_day: int = 1000):
        """Download 24 months of SPX options data."""
        logger.info("🚀 Starting 24-month SPX options download with price filtering")
        logger.info(f"📊 Price range: +/- {price_range} points from SPX")
        logger.info(f"🎯 Max contracts per day: {max_contracts_per_day}")
        
        if not self.api_key:
            logger.error("❌ API key not configured")
            return False
        
        start_time = datetime.now()
        
        # Calculate date range (24 months back)
        end_date = datetime.now()
        start_date = end_date - timedelta(days=24 * 30)  # Approximately 24 months
        
        start_str = start_date.strftime('%Y-%m-%d')
        end_str = end_date.strftime('%Y-%m-%d')
        
        logger.info(f"📅 Date range: {start_str} to {end_str}")
        
        # Download SPX prices first
        spx_df = self.download_spx_prices(start_str, end_str)
        if spx_df.empty:
            logger.error("❌ Failed to download SPX prices")
            return False
        
        logger.info(f"✅ Downloaded SPX prices for {len(spx_df)} trading days")
        
        # Download options data for each day
        all_data = []
        total_days = len(spx_df)
        failed_days = 0
        
        for i, (_, row) in enumerate(spx_df.iterrows()):
            date_str = row['date'].strftime('%Y-%m-%d')
            spx_price = row['spx_close']
            
            progress = f"[{i+1}/{total_days}]"
            
            try:
                day_data = self.download_day_data(date_str, spx_price, max_contracts_per_day)
                
                if not day_data.empty:
                    all_data.append(day_data)
                    
                    # Save daily backup (prioritize parquet)
                    daily_file = self.data_dir / f"spx_options_{date_str.replace('-', '')}.parquet"
                    try:
                        day_data.to_parquet(daily_file, index=False, engine='pyarrow', compression='snappy')
                        logger.debug(f"  💾 Saved daily backup (parquet): {daily_file}")
                    except Exception as e:
                        logger.warning(f"  ⚠️  Parquet save failed: {e}")
                        # Fallback to CSV
                        csv_file = daily_file.with_suffix('.csv')
                        day_data.to_csv(csv_file, index=False)
                        logger.debug(f"  💾 Saved daily backup as CSV: {csv_file}")
                else:
                    failed_days += 1
                    
            except Exception as e:
                logger.error(f"❌ Error processing {date_str}: {e}")
                failed_days += 1
                continue
            
            # Progress update every 10 days
            if (i + 1) % 10 == 0:
                success_rate = ((i + 1 - failed_days) / (i + 1) * 100)
                elapsed = (datetime.now() - start_time).total_seconds() / 60
                logger.info(f"📈 Progress: {i+1}/{total_days} days, {success_rate:.1f}% success, {elapsed:.1f}min elapsed, {self.api_calls_made} API calls")
        
        # Combine and save final dataset
        if all_data:
            logger.info("🔄 Combining all data into master file...")
            final_df = pd.concat(all_data, ignore_index=True)
            
            # Sort by date and time
            final_df = final_df.sort_values(['date', 'datetime']).reset_index(drop=True)
            
            # Save master file (prioritize parquet with compression)
            master_file = self.data_dir / "spx_options_24months_filtered.parquet"
            try:
                final_df.to_parquet(master_file, index=False, engine='pyarrow', compression='snappy')
                file_size_mb = master_file.stat().st_size / (1024 * 1024)
                logger.info(f"✅ Saved master file (parquet): {master_file} ({file_size_mb:.1f} MB)")
            except Exception as e:
                logger.warning(f"⚠️  Parquet save failed: {e}")
                # Fallback to CSV
                csv_file = master_file.with_suffix('.csv')
                final_df.to_csv(csv_file, index=False)
                file_size_mb = csv_file.stat().st_size / (1024 * 1024)
                logger.info(f"✅ Saved master file as CSV: {csv_file} ({file_size_mb:.1f} MB)")
            
            # Final statistics
            duration = (datetime.now() - start_time).total_seconds() / 60
            
            logger.info("🎉 DOWNLOAD COMPLETED!")
            logger.info(f"📊 Final Statistics:")
            logger.info(f"   Total 5-minute bars: {len(final_df):,}")
            logger.info(f"   Unique contracts: {final_df['contract'].nunique():,}")
            logger.info(f"   Date range: {final_df['date'].min()} to {final_df['date'].max()}")
            logger.info(f"   Trading days: {final_df['date'].nunique()}")
            logger.info(f"   Success rate: {((total_days - failed_days) / total_days * 100):.1f}%")
            logger.info(f"   Duration: {duration:.1f} minutes")
            logger.info(f"   API calls made: {self.api_calls_made:,}")
            logger.info(f"   Master file: {master_file}")
            
            return True
        else:
            logger.error("❌ No data downloaded")
            return False

def main():
    """Main function."""
    print("🚀 SPX Options 24-Month Download (+/- 200 from price)")
    print("=" * 60)
    
    downloader = SPX24MonthDownloader()
    
    if not downloader.api_key:
        print("❌ Please configure your POLYGON_API_KEY in the .env file")
        return False
    
    print(f"✅ API Key configured: ...{downloader.api_key[-4:]}")
    print("📊 This will download 24 months of SPX options data")
    print("🎯 Only contracts within +/- 200 points of daily SPX price")
    print("⏰ This will take several hours due to API rate limits")
    print("💾 Daily backups will be saved during download")
    
    # Get user preferences
    try:
        price_range = input(f"Price range (+/- points) [default: 200]: ").strip()
        price_range = int(price_range) if price_range else 200
        
        max_contracts = input(f"Max contracts per day [default: 1000]: ").strip()
        max_contracts = int(max_contracts) if max_contracts else 1000
        
    except ValueError:
        print("❌ Invalid input, using defaults")
        price_range = 200
        max_contracts = 1000
    
    print(f"\n🔍 Configuration:")
    print(f"  Price range: +/- {price_range} points")
    print(f"  Max contracts per day: {max_contracts}")
    print(f"  Estimated API calls: ~500,000+")
    
    response = input("\nProceed with download? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("❌ Download cancelled")
        return False
    
    success = downloader.download_24_months(price_range, max_contracts)
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
