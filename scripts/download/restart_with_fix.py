#!/usr/bin/env python3
"""
Restart SPX Download with Pandas.period Fix

This script helps restart the SPX options download with the fixed parquet saving.
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def check_current_download():
    """Check if there's a current download running."""
    try:
        # Check for running python processes with spx download
        result = subprocess.run(['pgrep', '-f', 'download_spx'], capture_output=True, text=True)
        if result.returncode == 0:
            pids = result.stdout.strip().split('\n')
            print(f"🔍 Found {len(pids)} running download processes:")
            for pid in pids:
                if pid:
                    print(f"  PID: {pid}")
            return pids
        else:
            print("✅ No running download processes found")
            return []
    except Exception as e:
        print(f"❌ Error checking processes: {e}")
        return []

def get_current_progress():
    """Get current progress from log files."""
    log_files = [
        'spx_optimized_download.log',
        'spx_24month_download.log',
        'spx_fixed_download.log'
    ]
    
    latest_progress = None
    
    for log_file in log_files:
        if Path(log_file).exists():
            try:
                with open(log_file, 'r') as f:
                    lines = f.readlines()
                
                # Look for latest progress line
                for line in reversed(lines):
                    if "Progress:" in line:
                        print(f"📊 Latest progress from {log_file}:")
                        print(f"   {line.strip()}")
                        latest_progress = line.strip()
                        break
                
                if latest_progress:
                    break
                    
            except Exception as e:
                print(f"⚠️  Could not read {log_file}: {e}")
    
    return latest_progress

def main():
    """Main restart function."""
    print("🔄 SPX Download Restart with Pandas.period Fix")
    print("=" * 50)
    
    # Check current status
    running_pids = check_current_download()
    progress = get_current_progress()
    
    if running_pids:
        print(f"\n⚠️  Current download is running with {len(running_pids)} processes")
        if progress:
            print("📈 Current progress shows the download is active")
        
        response = input("\nDo you want to stop the current download and restart with the fix? (y/N): ").strip().lower()
        
        if response in ['y', 'yes']:
            print("🛑 Stopping current download processes...")
            
            for pid in running_pids:
                if pid:
                    try:
                        subprocess.run(['kill', pid], check=True)
                        print(f"✅ Stopped process {pid}")
                    except subprocess.CalledProcessError:
                        print(f"⚠️  Could not stop process {pid} (may have already stopped)")
            
            print("⏳ Waiting 3 seconds for processes to stop...")
            time.sleep(3)
        else:
            print("❌ Restart cancelled - current download continues")
            return False
    
    # Check if fixed version exists
    fixed_script = Path("download_spx_optimized.py")
    if not fixed_script.exists():
        print("❌ Fixed download script not found!")
        return False
    
    print("✅ Fixed download script found")
    print("\n🚀 Starting download with pandas.period fix...")
    print("📋 The fixed version includes:")
    print("  • Pandas extension registry reset")
    print("  • Proper datetime string conversion")
    print("  • Explicit PyArrow schema handling")
    print("  • Robust error handling with CSV fallback")
    
    # Start the fixed download
    try:
        print(f"\n▶️  Executing: python3 {fixed_script}")
        
        # Run the fixed download script
        subprocess.run([sys.executable, str(fixed_script)], check=False)
        
        print("✅ Fixed download script completed")
        return True
        
    except KeyboardInterrupt:
        print("\n⏹️  Download interrupted by user")
        return False
    except Exception as e:
        print(f"❌ Error running fixed download: {e}")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎉 Download restart completed!")
        print("📁 Check the data/ directory for parquet files")
        print("📋 Monitor progress with: python3 monitor_download.py")
    else:
        print("\n❌ Restart failed or cancelled")
    
    sys.exit(0 if success else 1)
