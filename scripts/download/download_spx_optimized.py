#!/usr/bin/env python3
"""
Optimized SPX Options Data Download - 24 Months with 1000 contracts per day

This optimized version:
- Downloads up to 1000 contracts per day (default)
- Prioritizes contracts closest to ATM
- Uses efficient parquet storage with compression
- Provides better progress tracking and error handling
"""

import os
import sys
import json
import time
import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from pathlib import Path
import pytz
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock
import pyarrow as pa
import pyarrow.parquet as pq
import importlib

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('spx_optimized_download.log')
    ]
)
logger = logging.getLogger(__name__)

class OptimizedSPXDownloader:
    """Optimized SPX options data downloader with better efficiency."""
    
    def __init__(self):
        self.api_key = self._load_api_key()
        self.base_url = "https://api.polygon.io"
        self.rate_limit_delay = 0.11  # 110ms between requests
        self.max_workers = 8  # Increased threading
        self.data_dir = Path("data")
        self.data_dir.mkdir(exist_ok=True)

        # Timezone
        self.est_tz = pytz.timezone('US/Eastern')

        # Threading and progress tracking
        self.download_lock = Lock()
        self.api_calls_made = 0
        self.total_records = 0

        # Reset pandas extensions to avoid conflicts
        self._reset_pandas_extensions()
        
    def _load_api_key(self):
        """Load API key from .env file."""
        try:
            with open('.env', 'r') as f:
                for line in f:
                    if line.startswith('POLYGON_API_KEY='):
                        return line.split('=', 1)[1].strip()
        except FileNotFoundError:
            logger.error("❌ .env file not found!")
        return None

    def _reset_pandas_extensions(self):
        """Reset pandas extension registry to avoid conflicts."""
        try:
            import pyarrow.pandas_compat
            importlib.reload(pyarrow.pandas_compat)
            logger.debug("🔧 Reset pandas extension registry")
        except Exception as e:
            logger.debug(f"Could not reset extensions: {e}")

    def _clean_dataframe_for_parquet(self, df):
        """Clean DataFrame to avoid pandas/pyarrow type conflicts."""
        df_clean = df.copy()

        # Convert all datetime-like columns to strings
        for col in df_clean.columns:
            if 'datetime' in col or 'time' in col or df_clean[col].dtype == 'datetime64[ns]':
                df_clean[col] = df_clean[col].astype(str)
            elif hasattr(df_clean[col].dtype, 'name') and 'period' in str(df_clean[col].dtype):
                df_clean[col] = df_clean[col].astype(str)

        # Ensure numeric columns are proper types
        numeric_cols = ['open', 'high', 'low', 'close', 'bid', 'ask', 'spread', 'bid_size', 'ask_size', 'spx_price']
        for col in numeric_cols:
            if col in df_clean.columns:
                df_clean[col] = pd.to_numeric(df_clean[col], errors='coerce').astype('float64')

        # Ensure string columns are string type
        string_cols = ['contract', 'date']
        for col in string_cols:
            if col in df_clean.columns:
                df_clean[col] = df_clean[col].astype('string')

        return df_clean

    def _safe_parquet_save(self, df, filepath, compression='snappy'):
        """Safely save DataFrame to parquet with error handling."""
        try:
            # Reset extensions and clean dataframe
            self._reset_pandas_extensions()
            df_clean = self._clean_dataframe_for_parquet(df)

            # Create explicit schema
            schema_fields = []
            numeric_cols = ['open', 'high', 'low', 'close', 'bid', 'ask', 'spread', 'bid_size', 'ask_size', 'spx_price']

            for col in df_clean.columns:
                if col in numeric_cols:
                    schema_fields.append(pa.field(col, pa.float64()))
                else:
                    schema_fields.append(pa.field(col, pa.string()))

            schema = pa.schema(schema_fields)

            # Convert to pyarrow table and save
            table = pa.Table.from_pandas(df_clean, schema=schema, preserve_index=False)
            pq.write_table(table, filepath, compression=compression)

            return True

        except Exception as e:
            logger.debug(f"Parquet save failed: {e}")
            return False

    def download_spx_prices(self, start_date: str, end_date: str) -> pd.DataFrame:
        """Download SPX daily prices."""
        logger.info(f"📈 Downloading SPX prices from {start_date} to {end_date}")
        
        url = f"{self.base_url}/v2/aggs/ticker/I:SPX/range/1/day/{start_date}/{end_date}"
        params = {'apikey': self.api_key, 'sort': 'asc', 'limit': 50000}
        
        try:
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            data = response.json()
            
            if 'results' in data and data['results']:
                df = pd.DataFrame(data['results'])
                df['date'] = pd.to_datetime(df['t'], unit='ms').dt.date
                df = df.rename(columns={'o': 'spx_open', 'h': 'spx_high', 'l': 'spx_low', 'c': 'spx_close'})
                df = df[['date', 'spx_open', 'spx_high', 'spx_low', 'spx_close']].sort_values('date')
                
                logger.info(f"✅ Downloaded {len(df)} SPX price records")
                self.api_calls_made += 1
                return df
            else:
                logger.warning("⚠️  No SPX data found")
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"❌ Error downloading SPX prices: {e}")
            return pd.DataFrame()
    
    def get_prioritized_contracts(self, date: str, spx_price: float, price_range: int = 200, max_contracts: int = 1000) -> list:
        """Get options contracts prioritized by distance from ATM."""
        logger.debug(f"📊 Getting contracts for {date} (SPX: ${spx_price:,.2f})")
        
        min_strike = spx_price - price_range
        max_strike = spx_price + price_range
        
        date_obj = datetime.strptime(date, '%Y-%m-%d')
        exp_start = date_obj + timedelta(days=1)
        exp_end = date_obj + timedelta(days=30)
        
        all_contracts = []
        
        for contract_type in ['call', 'put']:
            url = f"{self.base_url}/v3/reference/options/contracts"
            params = {
                'underlying_ticker': 'SPX',
                'contract_type': contract_type,
                'as_of': date,
                'expiration_date.gte': exp_start.strftime('%Y-%m-%d'),
                'expiration_date.lte': exp_end.strftime('%Y-%m-%d'),
                'strike_price.gte': min_strike,
                'strike_price.lte': max_strike,
                'limit': 1000,
                'apikey': self.api_key
            }
            
            try:
                response = requests.get(url, params=params, timeout=30)
                response.raise_for_status()
                data = response.json()
                
                if 'results' in data and data['results']:
                    for contract in data['results']:
                        if 'ticker' in contract and 'strike_price' in contract:
                            strike = contract['strike_price']
                            if strike % 5 == 0:  # Standard strikes
                                distance = abs(strike - spx_price)
                                all_contracts.append((contract['ticker'], distance, strike))
                
                time.sleep(self.rate_limit_delay)
                self.api_calls_made += 1
                
            except Exception as e:
                logger.debug(f"Error getting {contract_type} contracts: {e}")
        
        # Sort by distance from ATM and take closest contracts
        all_contracts.sort(key=lambda x: x[1])  # Sort by distance
        prioritized_contracts = [c[0] for c in all_contracts[:max_contracts]]
        
        logger.info(f"  📊 Prioritized {len(prioritized_contracts)} contracts (closest to ATM)")
        return prioritized_contracts
    
    def download_contract_data(self, contract: str, date: str) -> pd.DataFrame:
        """Download 5-minute data for a contract."""
        try:
            date_obj = datetime.strptime(date, '%Y-%m-%d').date()
            market_open = self.est_tz.localize(datetime.combine(date_obj, datetime.min.time().replace(hour=9, minute=30)))
            market_close = self.est_tz.localize(datetime.combine(date_obj, datetime.min.time().replace(hour=16, minute=0)))
            
            open_timestamp = int(market_open.timestamp() * 1_000_000_000)
            close_timestamp = int(market_close.timestamp() * 1_000_000_000)
            
            url = f"{self.base_url}/v3/quotes/{contract}"
            params = {
                'timestamp.gte': open_timestamp,
                'timestamp.lt': close_timestamp,
                'order': 'asc',
                'limit': 5000,
                'sort': 'timestamp',
                'apikey': self.api_key
            }
            
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            data = response.json()
            
            if 'results' in data and data['results']:
                df = pd.DataFrame(data['results'])
                df['datetime_utc'] = pd.to_datetime(df['sip_timestamp'], unit='ns', utc=True)
                df['datetime'] = df['datetime_utc'].dt.tz_convert(self.est_tz)
                
                df['price'] = (df['bid_price'] + df['ask_price']) / 2
                df['spread'] = df['ask_price'] - df['bid_price']
                
                # Resample to 5-minute bars
                df = df.set_index('datetime')
                df_5min = df.groupby(pd.Grouper(freq='5min')).agg({
                    'price': ['first', 'max', 'min', 'last'],
                    'bid_price': 'last',
                    'ask_price': 'last',
                    'spread': 'mean',
                    'bid_size': 'last',
                    'ask_size': 'last'
                }).dropna()

                if not df_5min.empty:
                    df_5min.columns = ['open', 'high', 'low', 'close', 'bid', 'ask', 'spread', 'bid_size', 'ask_size']
                    df_5min = df_5min.reset_index()
                    df_5min['contract'] = contract
                    df_5min['date'] = date
                    # Convert datetime to string to avoid pandas/pyarrow issues
                    df_5min['datetime_str'] = df_5min['datetime'].dt.strftime('%Y-%m-%d %H:%M:%S%z')
                    df_5min['time_est'] = df_5min['datetime'].dt.strftime('%H:%M:%S')
                    # Keep original datetime for processing but will convert for saving
                    df_5min['datetime_original'] = df_5min['datetime']
                    
                    time.sleep(self.rate_limit_delay)
                    self.api_calls_made += 1
                    return df_5min
                
        except Exception as e:
            logger.debug(f"No data for {contract} on {date}: {e}")
        
        return pd.DataFrame()
    
    def download_day_data(self, date_str: str, spx_price: float, max_contracts: int = 1000) -> pd.DataFrame:
        """Download options data for a single day with optimized contract selection."""
        logger.info(f"📊 Processing {date_str} (SPX: ${spx_price:,.2f})")
        
        # Get prioritized contracts
        contracts = self.get_prioritized_contracts(date_str, spx_price, max_contracts=max_contracts)
        
        if not contracts:
            logger.warning(f"⚠️  No contracts found for {date_str}")
            return pd.DataFrame()
        
        # Download data with threading
        day_data = []
        successful_downloads = 0
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_contract = {
                executor.submit(self.download_contract_data, contract, date_str): contract 
                for contract in contracts
            }
            
            for future in as_completed(future_to_contract):
                contract = future_to_contract[future]
                try:
                    contract_data = future.result()
                    if not contract_data.empty:
                        contract_data['spx_price'] = spx_price
                        with self.download_lock:
                            day_data.append(contract_data)
                            self.total_records += len(contract_data)
                        successful_downloads += 1
                except Exception as e:
                    logger.debug(f"Error downloading {contract}: {e}")
        
        if day_data:
            day_df = pd.concat(day_data, ignore_index=True)
            logger.info(f"  ✅ {len(day_df)} records from {successful_downloads}/{len(contracts)} contracts")
            
            # Save daily backup using the fixed parquet method
            daily_file = self.data_dir / f"spx_options_{date_str.replace('-', '')}.parquet"

            if self._safe_parquet_save(day_df, daily_file):
                file_size_kb = daily_file.stat().st_size / 1024
                logger.info(f"  💾 ✅ Fixed parquet save: {daily_file} ({file_size_kb:.1f} KB)")
            else:
                # Fallback to CSV
                csv_file = daily_file.with_suffix('.csv')
                try:
                    day_df.to_csv(csv_file, index=False)
                    logger.info(f"  💾 📄 CSV fallback: {csv_file}")
                except Exception as csv_e:
                    logger.error(f"  ❌ CSV fallback failed: {csv_e}")
            
            return day_df
        else:
            logger.warning(f"  ⚠️  No data retrieved for {date_str}")
            return pd.DataFrame()
    
    def download_24_months(self, price_range: int = 200, max_contracts_per_day: int = 1000):
        """Download 24 months of optimized SPX options data."""
        logger.info("🚀 Starting OPTIMIZED 24-month SPX options download")
        logger.info(f"📊 Configuration: +/- {price_range} points, up to {max_contracts_per_day} contracts/day")
        
        if not self.api_key:
            logger.error("❌ API key not configured")
            return False
        
        start_time = datetime.now()
        
        # Calculate date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=24 * 30)
        
        start_str = start_date.strftime('%Y-%m-%d')
        end_str = end_date.strftime('%Y-%m-%d')
        
        logger.info(f"📅 Date range: {start_str} to {end_str}")
        
        # Download SPX prices
        spx_df = self.download_spx_prices(start_str, end_str)
        if spx_df.empty:
            logger.error("❌ Failed to download SPX prices")
            return False
        
        logger.info(f"✅ Downloaded SPX prices for {len(spx_df)} trading days")
        
        # Download options data
        all_data = []
        total_days = len(spx_df)
        failed_days = 0
        
        for i, (_, row) in enumerate(spx_df.iterrows()):
            date_str = row['date'].strftime('%Y-%m-%d')
            spx_price = row['spx_close']
            
            try:
                day_data = self.download_day_data(date_str, spx_price, max_contracts_per_day)
                
                if not day_data.empty:
                    all_data.append(day_data)
                else:
                    failed_days += 1
                    
            except Exception as e:
                logger.error(f"❌ Error processing {date_str}: {e}")
                failed_days += 1
                continue
            
            # Progress update every 5 days
            if (i + 1) % 5 == 0:
                success_rate = ((i + 1 - failed_days) / (i + 1) * 100)
                elapsed = (datetime.now() - start_time).total_seconds() / 60
                records_per_min = self.total_records / elapsed if elapsed > 0 else 0
                logger.info(f"📈 Progress: {i+1}/{total_days} days, {success_rate:.1f}% success, {self.total_records:,} records, {records_per_min:.0f} rec/min, {self.api_calls_made:,} API calls")
        
        # Save final dataset
        if all_data:
            logger.info("🔄 Combining and saving final dataset...")
            final_df = pd.concat(all_data, ignore_index=True)
            final_df = final_df.sort_values(['date', 'datetime']).reset_index(drop=True)
            
            # Save master file using the fixed parquet method
            master_file = self.data_dir / "spx_options_24months_optimized.parquet"

            if self._safe_parquet_save(final_df, master_file):
                file_size_mb = master_file.stat().st_size / (1024 * 1024)
                logger.info(f"✅ Fixed parquet master file: {master_file} ({file_size_mb:.1f} MB)")
            else:
                # Fallback to CSV
                csv_file = master_file.with_suffix('.csv')
                try:
                    final_df.to_csv(csv_file, index=False)
                    file_size_mb = csv_file.stat().st_size / (1024 * 1024)
                    logger.info(f"✅ CSV master file: {csv_file} ({file_size_mb:.1f} MB)")
                    master_file = csv_file
                except Exception as csv_e:
                    logger.error(f"❌ Master file save failed: {csv_e}")
                    return False
            
            # Final statistics
            duration = (datetime.now() - start_time).total_seconds() / 60
            
            logger.info("🎉 OPTIMIZED DOWNLOAD COMPLETED!")
            logger.info(f"📊 Final Statistics:")
            logger.info(f"   Total 5-minute bars: {len(final_df):,}")
            logger.info(f"   Unique contracts: {final_df['contract'].nunique():,}")
            logger.info(f"   Date range: {final_df['date'].min()} to {final_df['date'].max()}")
            logger.info(f"   Trading days: {final_df['date'].nunique()}")
            logger.info(f"   Success rate: {((total_days - failed_days) / total_days * 100):.1f}%")
            logger.info(f"   Duration: {duration:.1f} minutes")
            logger.info(f"   API calls: {self.api_calls_made:,}")
            logger.info(f"   File size: {file_size_mb:.1f} MB (parquet compressed)")
            logger.info(f"   Records per minute: {len(final_df) / duration:.0f}")
            
            return True
        else:
            logger.error("❌ No data downloaded")
            return False

def main():
    """Main function."""
    print("🚀 Optimized SPX Options 24-Month Download")
    print("=" * 50)
    print("✨ Features:")
    print("  • Up to 1000 contracts per day (prioritized by ATM distance)")
    print("  • Efficient parquet storage with compression")
    print("  • Enhanced threading and progress tracking")
    print("  • Daily backups during download")
    
    downloader = OptimizedSPXDownloader()
    
    if not downloader.api_key:
        print("❌ Please configure your POLYGON_API_KEY in the .env file")
        return False
    
    print(f"\n✅ API Key configured: ...{downloader.api_key[-4:]}")
    
    response = input("\nStart optimized 24-month download? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("❌ Download cancelled")
        return False
    
    success = downloader.download_24_months()
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
