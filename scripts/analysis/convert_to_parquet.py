#!/usr/bin/env python3
"""
Convert SPX Options CSV files to Parquet format

This script converts the downloaded CSV files to efficient parquet format
with proper data type handling to avoid pandas/pyarrow compatibility issues.
"""

import os
import pandas as pd
import numpy as np
from pathlib import Path
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def convert_csv_to_parquet(csv_file: Path, parquet_file: Path) -> bool:
    """Convert a single CSV file to parquet format."""
    try:
        # Read CSV
        df = pd.read_csv(csv_file)
        
        if df.empty:
            logger.warning(f"⚠️  Empty file: {csv_file}")
            return False
        
        # Fix data types to avoid pandas/pyarrow compatibility issues
        if 'datetime' in df.columns:
            # Convert datetime to string format
            df['datetime'] = pd.to_datetime(df['datetime']).dt.strftime('%Y-%m-%d %H:%M:%S%z')
        
        if 'time_est' in df.columns:
            # Convert time to string
            df['time_est'] = df['time_est'].astype(str)
        
        # Ensure numeric columns are proper types
        numeric_columns = ['open', 'high', 'low', 'close', 'bid', 'ask', 'spread', 'bid_size', 'ask_size', 'spx_price']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Save as parquet with compression
        df.to_parquet(parquet_file, index=False, engine='pyarrow', compression='snappy')
        
        # Check file sizes
        csv_size = csv_file.stat().st_size / 1024  # KB
        parquet_size = parquet_file.stat().st_size / 1024  # KB
        compression_ratio = csv_size / parquet_size if parquet_size > 0 else 0
        
        logger.info(f"✅ Converted {csv_file.name}: {csv_size:.1f}KB → {parquet_size:.1f}KB ({compression_ratio:.1f}x compression)")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to convert {csv_file}: {e}")
        return False

def combine_parquet_files(data_dir: Path, output_file: Path) -> bool:
    """Combine all parquet files into a single master file."""
    try:
        parquet_files = list(data_dir.glob("spx_options_*.parquet"))
        
        if not parquet_files:
            logger.warning("⚠️  No parquet files found to combine")
            return False
        
        logger.info(f"📊 Combining {len(parquet_files)} parquet files...")
        
        # Read and combine all parquet files
        dfs = []
        total_records = 0
        
        for pf in sorted(parquet_files):
            try:
                df = pd.read_parquet(pf)
                if not df.empty:
                    dfs.append(df)
                    total_records += len(df)
                    logger.debug(f"  Added {pf.name}: {len(df)} records")
            except Exception as e:
                logger.warning(f"  ⚠️  Failed to read {pf}: {e}")
        
        if not dfs:
            logger.error("❌ No valid parquet files to combine")
            return False
        
        # Combine all dataframes
        logger.info("🔄 Combining dataframes...")
        combined_df = pd.concat(dfs, ignore_index=True)
        
        # Sort by date and datetime
        if 'date' in combined_df.columns:
            combined_df = combined_df.sort_values(['date', 'datetime'] if 'datetime' in combined_df.columns else ['date'])
            combined_df = combined_df.reset_index(drop=True)
        
        # Save combined file
        combined_df.to_parquet(output_file, index=False, engine='pyarrow', compression='snappy')
        
        file_size_mb = output_file.stat().st_size / (1024 * 1024)
        
        logger.info(f"✅ Combined master file saved: {output_file}")
        logger.info(f"📊 Total records: {len(combined_df):,}")
        logger.info(f"📁 File size: {file_size_mb:.1f} MB")
        logger.info(f"🎯 Unique contracts: {combined_df['contract'].nunique():,}")
        logger.info(f"📅 Date range: {combined_df['date'].min()} to {combined_df['date'].max()}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to combine parquet files: {e}")
        return False

def main():
    """Main conversion function."""
    print("🔄 SPX Options CSV to Parquet Converter")
    print("=" * 45)
    
    data_dir = Path("data")
    
    if not data_dir.exists():
        print("❌ Data directory not found")
        return False
    
    # Find CSV files
    csv_files = list(data_dir.glob("spx_options_*.csv"))
    
    if not csv_files:
        print("❌ No SPX options CSV files found")
        return False
    
    print(f"📁 Found {len(csv_files)} CSV files to convert")
    
    # Convert each CSV to parquet
    successful_conversions = 0
    total_csv_size = 0
    total_parquet_size = 0
    
    for csv_file in sorted(csv_files):
        parquet_file = csv_file.with_suffix('.parquet')
        
        # Skip if parquet already exists and is newer
        if parquet_file.exists() and parquet_file.stat().st_mtime > csv_file.stat().st_mtime:
            logger.info(f"⏭️  Skipping {csv_file.name} (parquet already exists)")
            continue
        
        if convert_csv_to_parquet(csv_file, parquet_file):
            successful_conversions += 1
            total_csv_size += csv_file.stat().st_size
            total_parquet_size += parquet_file.stat().st_size
    
    if successful_conversions > 0:
        total_compression = total_csv_size / total_parquet_size if total_parquet_size > 0 else 0
        logger.info(f"✅ Converted {successful_conversions}/{len(csv_files)} files")
        logger.info(f"📊 Total compression: {total_compression:.1f}x")
        logger.info(f"💾 Space saved: {(total_csv_size - total_parquet_size) / (1024*1024):.1f} MB")
        
        # Create combined master file
        master_file = data_dir / "spx_options_24months_combined.parquet"
        if combine_parquet_files(data_dir, master_file):
            print(f"\n🎉 SUCCESS!")
            print(f"📁 Master file created: {master_file}")
            print(f"💡 You can now delete the individual CSV files to save space")
            
            # Show cleanup command
            print(f"\n🧹 To clean up CSV files, run:")
            print(f"   rm data/spx_options_*.csv")
            
            return True
    
    print("❌ No files were successfully converted")
    return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
