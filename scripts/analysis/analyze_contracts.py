#!/usr/bin/env python3
"""
Analyze SPX Options Contracts

This script analyzes the retrieved SPX options contracts and provides detailed insights.
"""

import pandas as pd
import numpy as np
from datetime import datetime
import matplotlib.pyplot as plt

def analyze_spx_contracts(csv_file):
    """Analyze the SPX contracts data."""
    
    print("📊 SPX Options Contracts Analysis")
    print("=" * 50)
    
    # Load data
    df = pd.read_csv(csv_file)
    print(f"✅ Loaded {len(df)} contracts from {csv_file}")
    
    # Basic statistics
    print(f"\n📈 Basic Statistics:")
    print(f"  Total Contracts: {len(df):,}")
    print(f"  Call Contracts: {len(df[df['contract_type'] == 'call']):,}")
    print(f"  Put Contracts: {len(df[df['contract_type'] == 'put']):,}")
    
    # Strike price analysis
    print(f"\n🎯 Strike Price Analysis:")
    print(f"  Strike Range: ${df['strike_price'].min():,.0f} - ${df['strike_price'].max():,.0f}")
    print(f"  Current SPX: ${6339.39:,.2f}")  # From our earlier fetch
    print(f"  Strike Increment: ${df['strike_price'].diff().dropna().mode().iloc[0]:,.0f}")
    
    # Expiration analysis
    df['expiration_date'] = pd.to_datetime(df['expiration_date'])
    print(f"\n📅 Expiration Analysis:")
    print(f"  Expiration Range: {df['expiration_date'].min().date()} to {df['expiration_date'].max().date()}")
    print(f"  Unique Expiration Dates: {df['expiration_date'].nunique()}")
    
    # Days to expiration
    today = datetime.now()
    df['days_to_expiration'] = (df['expiration_date'] - today).dt.days
    print(f"  Days to Expiration Range: {df['days_to_expiration'].min()} - {df['days_to_expiration'].max()}")
    
    # Show expiration distribution
    print(f"\n📊 Contracts by Expiration Date:")
    exp_summary = df.groupby(['expiration_date', 'contract_type']).size().unstack(fill_value=0)
    print(exp_summary)
    
    # ATM contracts (within 25 points)
    atm_contracts = df[df['distance_from_spot'] <= 25]
    print(f"\n🎯 At-The-Money Contracts (within 25 points):")
    print(f"  Total ATM Contracts: {len(atm_contracts)}")
    print(f"  ATM Calls: {len(atm_contracts[atm_contracts['contract_type'] == 'call'])}")
    print(f"  ATM Puts: {len(atm_contracts[atm_contracts['contract_type'] == 'put'])}")
    
    # Show closest to current price
    print(f"\n🎯 Contracts Closest to Current SPX Price:")
    closest = df.nsmallest(20, 'distance_from_spot')[['ticker', 'contract_type', 'strike_price', 'expiration_date', 'distance_from_spot']]
    print(closest.to_string(index=False))
    
    # Strike distribution
    print(f"\n📊 Strike Price Distribution:")
    strike_counts = df['strike_price'].value_counts().sort_index()
    print(f"  Most common strikes (top 10):")
    for strike, count in strike_counts.head(10).items():
        print(f"    ${strike:,.0f}: {count} contracts")
    
    # Moneyness analysis
    current_spx = 6339.39
    df['moneyness'] = df['strike_price'] / current_spx
    
    print(f"\n💰 Moneyness Analysis:")
    print(f"  Deep OTM (< 0.95): {len(df[df['moneyness'] < 0.95])}")
    print(f"  OTM (0.95 - 0.98): {len(df[(df['moneyness'] >= 0.95) & (df['moneyness'] < 0.98)])}")
    print(f"  Near ATM (0.98 - 1.02): {len(df[(df['moneyness'] >= 0.98) & (df['moneyness'] <= 1.02)])}")
    print(f"  ITM (1.02 - 1.05): {len(df[(df['moneyness'] > 1.02) & (df['moneyness'] <= 1.05)])}")
    print(f"  Deep ITM (> 1.05): {len(df[df['moneyness'] > 1.05])}")
    
    # Weekly vs Monthly options
    df['is_weekly'] = df['ticker'].str.contains('SPXW')
    print(f"\n📅 Weekly vs Monthly Options:")
    print(f"  Weekly Options (SPXW): {len(df[df['is_weekly']])}")
    print(f"  Monthly Options (SPX): {len(df[~df['is_weekly']])}")
    
    # Sample contracts for different strategies
    print(f"\n🎯 Sample Contracts for Common Strategies:")
    
    # ATM straddle
    atm_call = df[(df['contract_type'] == 'call') & (df['distance_from_spot'] <= 5)].iloc[0] if len(df[(df['contract_type'] == 'call') & (df['distance_from_spot'] <= 5)]) > 0 else None
    atm_put = df[(df['contract_type'] == 'put') & (df['distance_from_spot'] <= 5)].iloc[0] if len(df[(df['contract_type'] == 'put') & (df['distance_from_spot'] <= 5)]) > 0 else None
    
    if atm_call is not None and atm_put is not None:
        print(f"  ATM Straddle:")
        print(f"    Call: {atm_call['ticker']} (${atm_call['strike_price']:.0f})")
        print(f"    Put:  {atm_put['ticker']} (${atm_put['strike_price']:.0f})")
    
    # Iron Condor strikes
    otm_put = df[(df['contract_type'] == 'put') & (df['distance_from_spot'] >= 50) & (df['distance_from_spot'] <= 100)].iloc[0] if len(df[(df['contract_type'] == 'put') & (df['distance_from_spot'] >= 50) & (df['distance_from_spot'] <= 100)]) > 0 else None
    otm_call = df[(df['contract_type'] == 'call') & (df['distance_from_spot'] >= 50) & (df['distance_from_spot'] <= 100)].iloc[0] if len(df[(df['contract_type'] == 'call') & (df['distance_from_spot'] >= 50) & (df['distance_from_spot'] <= 100)]) > 0 else None
    
    if otm_put is not None and otm_call is not None:
        print(f"  Iron Condor (sample OTM strikes):")
        print(f"    OTM Put:  {otm_put['ticker']} (${otm_put['strike_price']:.0f})")
        print(f"    OTM Call: {otm_call['ticker']} (${otm_call['strike_price']:.0f})")
    
    return df

def create_visualization(df):
    """Create visualizations of the contracts data."""
    
    try:
        import matplotlib.pyplot as plt
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. Strike price distribution
        calls = df[df['contract_type'] == 'call']
        puts = df[df['contract_type'] == 'put']
        
        ax1.hist([calls['strike_price'], puts['strike_price']], 
                bins=30, alpha=0.7, label=['Calls', 'Puts'], color=['green', 'red'])
        ax1.axvline(x=6339.39, color='blue', linestyle='--', label='Current SPX')
        ax1.set_xlabel('Strike Price')
        ax1.set_ylabel('Number of Contracts')
        ax1.set_title('Strike Price Distribution')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. Expiration date distribution
        exp_counts = df['expiration_date'].value_counts().sort_index()
        ax2.bar(range(len(exp_counts)), exp_counts.values)
        ax2.set_xlabel('Expiration Date')
        ax2.set_ylabel('Number of Contracts')
        ax2.set_title('Contracts by Expiration Date')
        ax2.set_xticks(range(len(exp_counts)))
        ax2.set_xticklabels([d.strftime('%m/%d') for d in exp_counts.index], rotation=45)
        ax2.grid(True, alpha=0.3)
        
        # 3. Distance from spot distribution
        ax3.hist(df['distance_from_spot'], bins=30, alpha=0.7, color='purple')
        ax3.set_xlabel('Distance from Current SPX Price')
        ax3.set_ylabel('Number of Contracts')
        ax3.set_title('Distance from Spot Distribution')
        ax3.grid(True, alpha=0.3)
        
        # 4. Moneyness distribution by type
        calls_moneyness = calls['moneyness']
        puts_moneyness = puts['moneyness']
        
        ax4.hist([calls_moneyness, puts_moneyness], 
                bins=30, alpha=0.7, label=['Calls', 'Puts'], color=['green', 'red'])
        ax4.axvline(x=1.0, color='blue', linestyle='--', label='ATM')
        ax4.set_xlabel('Moneyness (Strike/Spot)')
        ax4.set_ylabel('Number of Contracts')
        ax4.set_title('Moneyness Distribution')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('spx_contracts_analysis.png', dpi=300, bbox_inches='tight')
        print(f"\n📊 Visualization saved as 'spx_contracts_analysis.png'")
        
    except ImportError:
        print(f"\n⚠️  Matplotlib not available - skipping visualization")

def main():
    """Main function."""
    
    # Find the most recent contracts file
    import glob
    contract_files = glob.glob("spx_contracts_*.csv")
    
    if not contract_files:
        print("❌ No contract files found. Run get_spx_contracts.py first.")
        return
    
    # Use the most recent file
    latest_file = max(contract_files)
    print(f"📁 Analyzing: {latest_file}")
    
    # Analyze contracts
    df = analyze_spx_contracts(latest_file)
    
    # Create visualization
    create_visualization(df)
    
    print(f"\n🎉 Analysis complete!")
    print(f"📊 {len(df)} SPX options contracts analyzed")
    print(f"📁 Data file: {latest_file}")

if __name__ == "__main__":
    main()
