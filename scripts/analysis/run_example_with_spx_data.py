#!/usr/bin/env python3
"""
Run Optionstrat Example with Downloaded SPX Options Data

This script demonstrates how to use the downloaded SPX options data
with the optionstrat program's genetic algorithm framework.
"""

import os
import sys
import pandas as pd
import numpy as np
import pytz
from datetime import datetime, timedelta
from pathlib import Path
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_spx_options_data():
    """Load all available SPX options parquet files."""
    
    data_dir = Path("data")
    parquet_files = list(data_dir.glob("spx_options_*.parquet"))
    
    if not parquet_files:
        logger.error("❌ No SPX options parquet files found!")
        logger.info("💡 Make sure the download has created some parquet files first")
        return None
    
    logger.info(f"📁 Found {len(parquet_files)} SPX options files")
    
    # Load and combine all parquet files
    dfs = []
    total_records = 0
    
    for pf in sorted(parquet_files):
        try:
            df = pd.read_parquet(pf)
            if not df.empty:
                dfs.append(df)
                total_records += len(df)
                logger.info(f"  ✅ Loaded {pf.name}: {len(df)} records")
        except Exception as e:
            logger.warning(f"  ⚠️  Failed to load {pf.name}: {e}")
    
    if not dfs:
        logger.error("❌ No valid parquet files could be loaded")
        return None
    
    # Combine all dataframes
    logger.info("🔄 Combining SPX options data...")
    combined_df = pd.concat(dfs, ignore_index=True)
    
    # Sort by date and datetime
    combined_df = combined_df.sort_values(['date', 'datetime']).reset_index(drop=True)
    
    logger.info(f"✅ Combined SPX options data:")
    logger.info(f"   Total records: {len(combined_df):,}")
    logger.info(f"   Date range: {combined_df['date'].min()} to {combined_df['date'].max()}")
    logger.info(f"   Unique contracts: {combined_df['contract'].nunique():,}")
    logger.info(f"   Trading days: {combined_df['date'].nunique()}")
    
    return combined_df

def analyze_spx_options_data(df):
    """Analyze the SPX options data for trading insights."""
    
    logger.info("📊 Analyzing SPX options data...")
    
    # Basic statistics
    print("\n" + "=" * 60)
    print("📊 SPX OPTIONS DATA ANALYSIS")
    print("=" * 60)
    
    print(f"📈 Dataset Overview:")
    print(f"   Records: {len(df):,}")
    print(f"   Contracts: {df['contract'].nunique():,}")
    print(f"   Date Range: {df['date'].min()} to {df['date'].max()}")
    print(f"   Trading Days: {df['date'].nunique()}")
    
    # Price analysis
    print(f"\n💰 Price Analysis:")
    print(f"   SPX Range: ${df['spx_price'].min():,.2f} - ${df['spx_price'].max():,.2f}")
    print(f"   Options Price Range: ${df['close'].min():.2f} - ${df['close'].max():,.2f}")
    print(f"   Average Spread: ${df['spread'].mean():.2f}")
    
    # Volume analysis
    print(f"\n📊 Volume Analysis:")
    print(f"   Avg Bid Size: {df['bid_size'].mean():.0f}")
    print(f"   Avg Ask Size: {df['ask_size'].mean():.0f}")
    print(f"   Total Volume Proxy: {(df['bid_size'] + df['ask_size']).sum():,.0f}")
    
    # Contract type analysis
    df['contract_type'] = df['contract'].str.extract(r'([CP])')
    calls = df[df['contract_type'] == 'C']
    puts = df[df['contract_type'] == 'P']
    
    print(f"\n🎯 Contract Analysis:")
    print(f"   Call Options: {len(calls):,} records ({len(calls)/len(df)*100:.1f}%)")
    print(f"   Put Options: {len(puts):,} records ({len(puts)/len(df)*100:.1f}%)")
    
    # Extract strike prices for moneyness analysis
    df['strike'] = df['contract'].str.extract(r'([CP])(\d{8})').iloc[:, 1].astype(float) / 1000
    df['moneyness'] = df['strike'] / df['spx_price']
    
    print(f"\n💎 Moneyness Analysis:")
    print(f"   Deep OTM (< 0.95): {len(df[df['moneyness'] < 0.95]):,}")
    print(f"   OTM (0.95-0.98): {len(df[(df['moneyness'] >= 0.95) & (df['moneyness'] < 0.98)]):,}")
    print(f"   Near ATM (0.98-1.02): {len(df[(df['moneyness'] >= 0.98) & (df['moneyness'] <= 1.02)]):,}")
    print(f"   ITM (1.02-1.05): {len(df[(df['moneyness'] > 1.02) & (df['moneyness'] <= 1.05)]):,}")
    print(f"   Deep ITM (> 1.05): {len(df[df['moneyness'] > 1.05]):,}")
    
    return df

def create_sample_strategies(df):
    """Create sample trading strategies using the SPX options data."""
    
    logger.info("🎯 Creating sample trading strategies...")
    
    print(f"\n🚀 SAMPLE TRADING STRATEGIES")
    print("=" * 40)
    
    # Strategy 1: ATM Straddle Analysis
    print(f"\n📊 Strategy 1: ATM Straddle Analysis")
    atm_options = df[(df['moneyness'] >= 0.99) & (df['moneyness'] <= 1.01)]
    
    if not atm_options.empty:
        avg_atm_price = atm_options['close'].mean()
        avg_atm_spread = atm_options['spread'].mean()
        
        print(f"   ATM Options: {len(atm_options):,} records")
        print(f"   Average ATM Price: ${avg_atm_price:.2f}")
        print(f"   Average ATM Spread: ${avg_atm_spread:.2f}")
        print(f"   Spread as % of Price: {(avg_atm_spread/avg_atm_price)*100:.2f}%")
    
    # Strategy 2: Iron Condor Setup
    print(f"\n📊 Strategy 2: Iron Condor Analysis")
    otm_puts = df[(df['contract_type'] == 'P') & (df['moneyness'] >= 0.90) & (df['moneyness'] <= 0.95)]
    otm_calls = df[(df['contract_type'] == 'C') & (df['moneyness'] >= 1.05) & (df['moneyness'] <= 1.10)]
    
    if not otm_puts.empty and not otm_calls.empty:
        avg_put_price = otm_puts['close'].mean()
        avg_call_price = otm_calls['close'].mean()
        
        print(f"   OTM Puts: {len(otm_puts):,} records, Avg Price: ${avg_put_price:.2f}")
        print(f"   OTM Calls: {len(otm_calls):,} records, Avg Price: ${avg_call_price:.2f}")
        print(f"   Iron Condor Credit: ${avg_put_price + avg_call_price:.2f}")
    
    # Strategy 3: Volatility Analysis
    print(f"\n📊 Strategy 3: Volatility Analysis")
    
    # Calculate daily price changes
    daily_data = df.groupby('date').agg({
        'spx_price': 'first',
        'close': 'mean',
        'spread': 'mean'
    }).reset_index()
    
    daily_data['spx_change'] = daily_data['spx_price'].pct_change()
    daily_data['options_change'] = daily_data['close'].pct_change()
    
    if len(daily_data) > 1:
        spx_volatility = daily_data['spx_change'].std() * np.sqrt(252) * 100
        options_volatility = daily_data['options_change'].std() * np.sqrt(252) * 100
        
        print(f"   SPX Annualized Volatility: {spx_volatility:.1f}%")
        print(f"   Options Price Volatility: {options_volatility:.1f}%")
        print(f"   Average Daily SPX Change: {daily_data['spx_change'].mean()*100:.2f}%")
    
    return daily_data

def demonstrate_genetic_algorithm_integration(df):
    """Demonstrate how to integrate SPX options data with genetic algorithms."""
    
    logger.info("🧬 Demonstrating genetic algorithm integration...")
    
    print(f"\n🧬 GENETIC ALGORITHM INTEGRATION")
    print("=" * 45)
    
    try:
        # Try to import the genetic algorithm components
        from quantlab.gene import GeneticAlpha, Gene
        
        print("✅ Genetic algorithm components available")
        
        # Create a simple dataset for genetic algorithm
        # Group by date to create daily summaries
        daily_summary = df.groupby('date').agg({
            'spx_price': 'first',
            'close': 'mean',
            'spread': 'mean',
            'bid_size': 'mean',
            'ask_size': 'mean'
        }).reset_index()
        
        # Convert to the format expected by genetic algorithms
        daily_summary.index = pd.to_datetime(daily_summary['date'])
        
        print(f"📊 Created daily summary: {len(daily_summary)} trading days")
        print(f"   Date range: {daily_summary['date'].min()} to {daily_summary['date'].max()}")
        
        # Example genetic algorithm strategy
        print(f"\n🎯 Example Strategy: Options Price Momentum")
        
        # Create a simple momentum strategy based on options prices
        daily_summary['options_ma_5'] = daily_summary['close'].rolling(5).mean()
        daily_summary['options_ma_20'] = daily_summary['close'].rolling(20).mean()
        daily_summary['momentum_signal'] = (daily_summary['options_ma_5'] > daily_summary['options_ma_20']).astype(int)
        
        # Calculate returns
        daily_summary['spx_returns'] = daily_summary['spx_price'].pct_change()
        daily_summary['strategy_returns'] = daily_summary['momentum_signal'].shift(1) * daily_summary['spx_returns']
        
        # Performance metrics
        total_return = (1 + daily_summary['strategy_returns'].fillna(0)).prod() - 1
        sharpe_ratio = daily_summary['strategy_returns'].mean() / daily_summary['strategy_returns'].std() * np.sqrt(252)
        
        print(f"   Total Return: {total_return*100:.2f}%")
        print(f"   Sharpe Ratio: {sharpe_ratio:.2f}")
        print(f"   Win Rate: {(daily_summary['strategy_returns'] > 0).mean()*100:.1f}%")
        
        return daily_summary
        
    except ImportError:
        print("⚠️  Genetic algorithm components not available")
        print("💡 This would integrate with the quantlab.gene module")
        print("📋 Example integration points:")
        print("   • Use SPX options data as alternative data source")
        print("   • Create options-based alpha factors")
        print("   • Combine with stock data for multi-asset strategies")
        
        return None

def main():
    """Main example function."""
    
    print("🚀 Optionstrat Example with SPX Options Data")
    print("=" * 55)
    
    # Load SPX options data
    spx_data = load_spx_options_data()
    
    if spx_data is None:
        print("❌ No SPX options data available")
        print("💡 Wait for the download to create some parquet files first")
        return False
    
    # Analyze the data
    analyzed_data = analyze_spx_options_data(spx_data)
    
    # Create sample strategies
    daily_data = create_sample_strategies(analyzed_data)
    
    # Demonstrate genetic algorithm integration
    ga_data = demonstrate_genetic_algorithm_integration(analyzed_data)
    
    # Summary
    print(f"\n🎉 EXAMPLE COMPLETED SUCCESSFULLY!")
    print("=" * 40)
    print(f"✅ Loaded and analyzed {len(spx_data):,} SPX options records")
    print(f"📊 Demonstrated multiple trading strategies")
    print(f"🧬 Showed genetic algorithm integration potential")
    print(f"💾 Data ready for advanced quantitative analysis")
    
    print(f"\n📋 Next Steps:")
    print(f"   • Use this data with quantlab.gene for strategy development")
    print(f"   • Combine with stock data for multi-asset strategies")
    print(f"   • Implement real-time options pricing models")
    print(f"   • Develop volatility-based trading signals")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
