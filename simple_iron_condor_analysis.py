#!/usr/bin/env python3
"""
Simple Iron Condor P&L Analysis

This script provides a simplified analysis of iron condor P&L using our SPX options data.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_sample_data():
    """Load a sample of SPX options data."""
    
    data_dir = Path("data")
    
    # Try master file first
    master_file = data_dir / "spx_options_24months_optimized.parquet"
    if master_file.exists():
        df = pd.read_parquet(master_file)
        logger.info(f"✅ Loaded {len(df):,} records from master file")
        return df.head(50000)  # Sample for analysis
    
    # Fallback to individual files
    parquet_files = list(data_dir.glob("spx_options_*.parquet"))
    if parquet_files:
        df = pd.read_parquet(parquet_files[-1])
        logger.info(f"✅ Loaded {len(df):,} records from {parquet_files[-1].name}")
        return df
    
    return None

def analyze_data_structure(df):
    """Analyze the structure of our options data."""
    
    print("🔍 DATA STRUCTURE ANALYSIS")
    print("=" * 40)
    
    # Parse contract types
    df['contract_type'] = df['contract'].str.extract(r'([CP])')[0]
    df['strike'] = df['contract'].str.extract(r'([CP])(\d{8})')[1].astype(float) / 1000
    
    print(f"📊 Dataset Overview:")
    print(f"   Total Records: {len(df):,}")
    print(f"   Unique Dates: {df['date'].nunique()}")
    print(f"   Unique Contracts: {df['contract'].nunique():,}")
    
    print(f"\n🎯 Contract Types:")
    type_counts = df['contract_type'].value_counts()
    for contract_type, count in type_counts.items():
        type_name = 'Calls' if contract_type == 'C' else 'Puts'
        print(f"   {type_name}: {count:,} ({count/len(df)*100:.1f}%)")
    
    print(f"\n💰 Price Analysis:")
    print(f"   SPX Range: ${df['spx_price'].min():.2f} - ${df['spx_price'].max():.2f}")
    print(f"   Strike Range: ${df['strike'].min():.0f} - ${df['strike'].max():.0f}")
    print(f"   Options Price Range: ${df['close'].min():.2f} - ${df['close'].max():.2f}")
    
    return df

def create_theoretical_iron_condor():
    """Create a theoretical iron condor P&L analysis."""
    
    print(f"\n📊 THEORETICAL IRON CONDOR P&L ANALYSIS")
    print("=" * 50)
    
    # Theoretical iron condor parameters
    current_spx = 5500  # Example SPX price
    
    # Iron condor strikes (typical setup)
    short_put_strike = current_spx * 0.95    # 5% OTM put (sell)
    long_put_strike = short_put_strike - 50  # Further OTM put (buy)
    short_call_strike = current_spx * 1.05   # 5% OTM call (sell)
    long_call_strike = short_call_strike + 50 # Further OTM call (buy)
    
    # Theoretical premiums (based on typical SPX options)
    short_put_premium = 25.0   # Sell put premium
    long_put_premium = 15.0    # Buy put premium
    short_call_premium = 30.0  # Sell call premium
    long_call_premium = 20.0   # Buy call premium
    
    # Net credit received
    net_credit = short_put_premium + short_call_premium - long_put_premium - long_call_premium
    
    print(f"🎯 Iron Condor Setup:")
    print(f"   Current SPX: ${current_spx:,.0f}")
    print(f"   Short Put Strike: ${short_put_strike:,.0f} (Premium: ${short_put_premium:.2f})")
    print(f"   Long Put Strike: ${long_put_strike:,.0f} (Premium: ${long_put_premium:.2f})")
    print(f"   Short Call Strike: ${short_call_strike:,.0f} (Premium: ${short_call_premium:.2f})")
    print(f"   Long Call Strike: ${long_call_strike:,.0f} (Premium: ${long_call_premium:.2f})")
    print(f"   Net Credit Received: ${net_credit:.2f}")
    
    # Calculate P&L at various expiration prices
    expiration_prices = np.arange(current_spx * 0.85, current_spx * 1.15, 10)
    pnl_results = []
    
    for exp_price in expiration_prices:
        # Put spread P&L
        if exp_price <= long_put_strike:
            put_pnl = -(short_put_strike - long_put_strike)  # Max loss
        elif exp_price >= short_put_strike:
            put_pnl = 0  # No assignment
        else:
            put_pnl = -(short_put_strike - exp_price)  # Partial loss
        
        # Call spread P&L
        if exp_price >= long_call_strike:
            call_pnl = -(long_call_strike - short_call_strike)  # Max loss
        elif exp_price <= short_call_strike:
            call_pnl = 0  # No assignment
        else:
            call_pnl = -(exp_price - short_call_strike)  # Partial loss
        
        # Total P&L
        total_pnl = net_credit + put_pnl + call_pnl
        
        pnl_results.append({
            'expiration_price': exp_price,
            'put_pnl': put_pnl,
            'call_pnl': call_pnl,
            'total_pnl': total_pnl
        })
    
    pnl_df = pd.DataFrame(pnl_results)
    
    # Calculate key metrics
    max_profit = pnl_df['total_pnl'].max()
    max_loss = pnl_df['total_pnl'].min()
    breakeven_lower = short_put_strike - net_credit
    breakeven_upper = short_call_strike + net_credit
    
    print(f"\n💰 P&L Metrics:")
    print(f"   Maximum Profit: ${max_profit:.2f}")
    print(f"   Maximum Loss: ${max_loss:.2f}")
    print(f"   Profit/Loss Ratio: {abs(max_profit/max_loss):.2f}")
    print(f"   Lower Breakeven: ${breakeven_lower:.2f}")
    print(f"   Upper Breakeven: ${breakeven_upper:.2f}")
    print(f"   Profit Zone: ${breakeven_lower:.2f} - ${breakeven_upper:.2f}")
    print(f"   Profit Zone Width: ${breakeven_upper - breakeven_lower:.2f} points")
    
    # Plot P&L diagram
    plt.figure(figsize=(12, 8))
    
    plt.plot(pnl_df['expiration_price'], pnl_df['total_pnl'], 
             linewidth=3, color='blue', label='Iron Condor P&L')
    
    # Mark current price and strikes
    plt.axvline(x=current_spx, color='red', linestyle='--', alpha=0.7, 
                label=f'Current SPX: ${current_spx:,.0f}')
    plt.axvline(x=short_put_strike, color='green', linestyle=':', alpha=0.7,
                label=f'Short Put: ${short_put_strike:,.0f}')
    plt.axvline(x=short_call_strike, color='orange', linestyle=':', alpha=0.7,
                label=f'Short Call: ${short_call_strike:,.0f}')
    
    # Mark breakeven points
    plt.axvline(x=breakeven_lower, color='purple', linestyle='-.', alpha=0.7,
                label=f'Lower BE: ${breakeven_lower:.0f}')
    plt.axvline(x=breakeven_upper, color='purple', linestyle='-.', alpha=0.7,
                label=f'Upper BE: ${breakeven_upper:.0f}')
    
    # Profit/loss zones
    plt.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    plt.fill_between(pnl_df['expiration_price'], pnl_df['total_pnl'], 0,
                     where=(pnl_df['total_pnl'] > 0), alpha=0.3, color='green', label='Profit Zone')
    plt.fill_between(pnl_df['expiration_price'], pnl_df['total_pnl'], 0,
                     where=(pnl_df['total_pnl'] < 0), alpha=0.3, color='red', label='Loss Zone')
    
    plt.xlabel('SPX Price at Expiration')
    plt.ylabel('Profit/Loss ($)')
    plt.title('Iron Condor P&L Diagram - Theoretical Analysis')
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    
    # Save plot
    plt.savefig('theoretical_iron_condor_pnl.png', dpi=300, bbox_inches='tight')
    logger.info("💾 Saved P&L chart: theoretical_iron_condor_pnl.png")
    plt.show()
    
    return pnl_df

def analyze_real_data_iron_condor_potential(df):
    """Analyze real data for iron condor potential."""
    
    print(f"\n🔍 REAL DATA IRON CONDOR ANALYSIS")
    print("=" * 45)
    
    # Get a sample day
    sample_date = df['date'].iloc[0]
    day_data = df[df['date'] == sample_date].copy()
    
    if day_data.empty:
        print("❌ No data available for analysis")
        return
    
    current_spx = day_data['spx_price'].iloc[0]
    
    # Parse contract information
    day_data['contract_type'] = day_data['contract'].str.extract(r'([CP])')[0]
    day_data['strike'] = day_data['contract'].str.extract(r'([CP])(\d{8})')[1].astype(float) / 1000
    day_data['moneyness'] = day_data['strike'] / current_spx
    
    print(f"📅 Analysis Date: {sample_date}")
    print(f"💰 SPX Price: ${current_spx:.2f}")
    print(f"📊 Available Options: {len(day_data):,}")
    
    # Analyze available strikes for iron condor
    calls = day_data[day_data['contract_type'] == 'C']
    puts = day_data[day_data['contract_type'] == 'P']
    
    print(f"\n🎯 Available Contracts:")
    print(f"   Calls: {len(calls):,}")
    print(f"   Puts: {len(puts):,}")
    
    if len(calls) > 0:
        print(f"   Call Strike Range: ${calls['strike'].min():.0f} - ${calls['strike'].max():.0f}")
        print(f"   Call Price Range: ${calls['close'].min():.2f} - ${calls['close'].max():.2f}")
    
    if len(puts) > 0:
        print(f"   Put Strike Range: ${puts['strike'].min():.0f} - ${puts['strike'].max():.0f}")
        print(f"   Put Price Range: ${puts['close'].min():.2f} - ${puts['close'].max():.2f}")
    
    # Find potential iron condor legs
    if len(calls) > 0 and len(puts) > 0:
        # Look for OTM options suitable for iron condor
        otm_puts = puts[puts['moneyness'] < 0.98]
        otm_calls = calls[calls['moneyness'] > 1.02]
        
        print(f"\n🔧 Iron Condor Potential:")
        print(f"   OTM Puts Available: {len(otm_puts)}")
        print(f"   OTM Calls Available: {len(otm_calls)}")
        
        if len(otm_puts) > 0:
            print(f"   Put Strike Range: ${otm_puts['strike'].min():.0f} - ${otm_puts['strike'].max():.0f}")
            print(f"   Put Premium Range: ${otm_puts['close'].min():.2f} - ${otm_puts['close'].max():.2f}")
        
        if len(otm_calls) > 0:
            print(f"   Call Strike Range: ${otm_calls['strike'].min():.0f} - ${otm_calls['strike'].max():.0f}")
            print(f"   Call Premium Range: ${otm_calls['close'].min():.2f} - ${otm_calls['close'].max():.2f}")
        
        # Estimate potential iron condor credit
        if len(otm_puts) > 0 and len(otm_calls) > 0:
            avg_put_premium = otm_puts['close'].mean()
            avg_call_premium = otm_calls['close'].mean()
            estimated_credit = avg_put_premium + avg_call_premium
            
            print(f"\n💰 Estimated Iron Condor Metrics:")
            print(f"   Average Put Premium: ${avg_put_premium:.2f}")
            print(f"   Average Call Premium: ${avg_call_premium:.2f}")
            print(f"   Estimated Gross Credit: ${estimated_credit:.2f}")
            print(f"   Estimated Net Credit: ${estimated_credit * 0.7:.2f} (after buying protection)")

def main():
    """Main analysis function."""
    
    print("📊 Iron Condor P&L Analysis with SPX Options Data")
    print("=" * 60)
    
    # Load data
    df = load_sample_data()
    
    if df is None:
        print("❌ No SPX options data available")
        return False
    
    # Analyze data structure
    analyzed_df = analyze_data_structure(df)
    
    # Create theoretical iron condor analysis
    theoretical_pnl = create_theoretical_iron_condor()
    
    # Analyze real data potential
    analyze_real_data_iron_condor_potential(analyzed_df)
    
    print(f"\n🎉 IRON CONDOR ANALYSIS COMPLETE!")
    print("=" * 40)
    print(f"✅ Analyzed theoretical iron condor P&L")
    print(f"📊 Generated P&L diagram")
    print(f"🔍 Examined real SPX options data")
    print(f"💡 Identified iron condor opportunities")
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
