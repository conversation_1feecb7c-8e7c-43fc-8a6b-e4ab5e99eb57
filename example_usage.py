#!/usr/bin/env python3
"""
Optionstrat Program - Integrated Data Downloader Example Usage

This example shows how to use the integrated data downloader in the optionstrat program,
including both stock data and SPX options data.
"""

import sys
import os
import pandas as pd
import pytz
from datetime import datetime, timed<PERSON>ta
from pathlib import Path

# Import the integrated data manager
try:
    from quantlab.data.data_integration import create_data_manager
    from quantlab.data.data_downloader import SPXDataDownloader
    INTEGRATION_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Data integration not available: {e}")
    INTEGRATION_AVAILABLE = False

# Import logging
try:
    from quantlab.utils.logging_config import setup_optionstrat_logging
    setup_optionstrat_logging(log_level="INFO")
except ImportError:
    import logging
    logging.basicConfig(level=logging.INFO)

def example_integrated_data_manager():
    """Example usage of the integrated data manager."""

    print("🚀 Integrated Data Manager Example")
    print("=" * 50)

    if not INTEGRATION_AVAILABLE:
        print("❌ Data integration not available")
        return

    try:
        # Create data manager
        data_manager = create_data_manager(cache_enabled=True)
        print("✅ Data manager initialized")

        # Get data summary
        print("\n📊 Data Source Summary:")
        summary = data_manager.get_data_summary()
        for key, value in summary.items():
            print(f"  {key}: {value}")

        # Example 1: Get stock data only
        print("\n📈 Example 1: Stock Data Only")
        start_date = datetime(2023, 1, 1, tzinfo=pytz.utc)
        end_date = datetime(2023, 6, 30, tzinfo=pytz.utc)

        tickers = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA']  # Sample tickers
        valid_tickers, ticker_dfs = data_manager.get_stock_data(tickers, start_date, end_date)

        print(f"✅ Retrieved data for {len(valid_tickers)} tickers")
        for ticker in valid_tickers[:3]:  # Show first 3
            df = ticker_dfs[ticker]
            print(f"  {ticker}: {len(df)} records, {df.index.min()} to {df.index.max()}")

        # Example 2: Combined dataset with SPX options
        print("\n📊 Example 2: Combined Dataset (Stock + SPX Options)")
        dataset = data_manager.get_combined_dataset(
            tickers=tickers,
            start_date=start_date,
            end_date=end_date,
            include_spx_options=True,
            spx_download_mode="incremental"
        )

        # Show stock data
        stock_data = dataset['stock_data']
        print(f"✅ Stock data: {len(stock_data['tickers'])} tickers")

        # Show SPX options data
        spx_data = dataset.get('spx_options_data')
        if spx_data is not None:
            print(f"✅ SPX options data: {len(spx_data)} records")
            print(f"  Date range: {spx_data['date'].min()} to {spx_data['date'].max()}")
            print(f"  Unique contracts: {spx_data['contract'].nunique()}")
            print(f"  Columns: {list(spx_data.columns)}")
        else:
            print("⚠️  No SPX options data available")

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


def example_direct_spx_downloader():
    """Example usage of the SPX data downloader directly."""

    print("\n🎯 Direct SPX Data Downloader Example")
    print("=" * 50)

    try:
        # Initialize the downloader
        downloader = SPXDataDownloader()
        print("✅ SPX data downloader initialized")

        # Example: Download 1 month of data
        print("\n📊 Downloading 1 month of SPX options data...")
        success = downloader.full_download(months=1, calculate_greeks=True)

        if success:
            print("✅ Download completed successfully!")

            # Check the results
            master_file = downloader.file_paths['master_file']
            if os.path.exists(master_file):
                df = pd.read_parquet(master_file)
                print(f"📈 Downloaded {len(df):,} 5-minute bars")
                print(f"📅 Date range: {df['date'].min()} to {df['date'].max()}")
                print(f"🎯 Unique contracts: {df['contract'].nunique():,}")

                # Show sample of data
                print(f"\n📋 Sample data (first 5 rows):")
                print(df.head())

        else:
            print("❌ Download failed")

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


def example_main_program_integration():
    """Example showing how to use the main program with integrated data."""

    print("\n🔗 Main Program Integration Example")
    print("=" * 50)

    try:
        # Import main program functions
        from main import get_integrated_data, main

        # Example 1: Get integrated data
        print("📊 Getting integrated data...")
        start_date = datetime(2023, 1, 1, tzinfo=pytz.utc)
        end_date = datetime(2023, 3, 31, tzinfo=pytz.utc)

        dataset = get_integrated_data(
            start=start_date,
            end=end_date,
            tickers=['AAPL', 'MSFT', 'GOOGL'],
            include_spx_options=False,  # Set to True to include SPX options
            use_cache=True
        )

        stock_data = dataset['stock_data']
        print(f"✅ Retrieved stock data for {len(stock_data['tickers'])} tickers")

        # Example 2: Run main program with different configurations
        print("\n🚀 Running main program examples...")

        # Run with legacy data only
        print("  Running with legacy data...")
        # main(use_integrated_data=False, include_spx_options=False)

        # Run with integrated data (uncomment to actually run)
        print("  Would run with integrated data...")
        # main(use_integrated_data=True, include_spx_options=False)

        print("✅ Main program integration examples completed")

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


def main():
    """Run all examples."""
    print("🚀 Optionstrat Integrated Data Downloader Examples")
    print("=" * 60)

    # Run examples
    example_integrated_data_manager()

    if INTEGRATION_AVAILABLE:
        example_direct_spx_downloader()

    example_main_program_integration()

    print("\n✅ All examples completed!")
    print("\n📚 Usage Tips:")
    print("  - Use integrated data manager for most use cases")
    print("  - Enable caching to speed up repeated data requests")
    print("  - Check logs in ./logs/ directory for detailed information")
    print("  - Use environment variables in .env file for configuration")
    print("  - Run 'python main.py --data-summary' to see data source status")

if __name__ == "__main__":
    main()
