import numpy as np 
import pandas as pd 
import seaborn as sns 
import matplotlib.pyplot as plt 

from quantyhlib.general_utils import load_pickle
def pnl_attribution(simdf,plot=False, path="/images"):
    load_pickle("pnl.pickle",(simdf,plot,path))
    strat_und,strat_opt,other,hedge,total=\
        simdf.stratund_pnl,simdf.stratopt_pnl,simdf.other_pnl,simdf.hedge_pnl,simdf.pnl
    capital = simdf.capital.shift(1).fillna(0)
    attribution= lambda pnl: (1+pnl/capital).cumprod()
    if plot:
        import os
        from pathlib import Path
        Path(os.path.abspath(os.getcwd()+path)).mkdir(parents=True,exist_ok=True)
        sns.lineplot(attribution(strat_und),label="strat underlying")
        sns.lineplot(attribution(strat_opt),label="strat option")
        sns.lineplot(attribution(other),label="other underlying")
        sns.lineplot(attribution(hedge),label="hedge")
        sns.lineplot(attribution(total),label="total")
        plt.savefig(f".{path}/attribution.png")
        plt.close()
    return {
        "pnl_strat_underlying": attribution(strat_und),
        "pnl_strat_opt": attribution(strat_opt),
        "pnl_strat_other": attribution(other),
        "pnl_strat_hedge": attribution(hedge),
        "pnl_strat_total": attribution(total),
    }

def risk_attribution(capital,logs,plot=False,path="/images"):
    pass