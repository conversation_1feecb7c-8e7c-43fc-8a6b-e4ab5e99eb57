#!/usr/bin/env python3
"""
Data Integration Module for OptionstratProgram

This module provides a unified interface for integrating different data sources
including the existing yfinance-based data fetching and the new SPX options data downloader.
"""

import os
import logging
import pandas as pd
import pytz
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple, Union
from pathlib import Path

# Import existing utilities
from quantlab.utils import save_pickle, load_pickle

# Import logging configuration
try:
    from quantlab.utils.logging_config import get_contextual_logger, log_function_call
    logger = get_contextual_logger(__name__, "DataIntegration")
except ImportError:
    logger = logging.getLogger(__name__)


class DataIntegrationManager:
    """
    Manages integration between different data sources in the optionstrat program.
    
    Provides a unified interface for:
    - Existing yfinance-based stock data
    - New SPX options data downloader
    - Data caching and persistence
    - Error handling and fallback mechanisms
    """
    
    def __init__(self, data_dir: str = "data", cache_enabled: bool = True):
        """
        Initialize the data integration manager.
        
        Args:
            data_dir: Directory for storing data files
            cache_enabled: Whether to enable data caching
        """
        self.data_dir = Path(data_dir)
        self.cache_enabled = cache_enabled
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Ensure data directory exists
        self.data_dir.mkdir(exist_ok=True)
        
        # Initialize data sources
        self._spx_downloader = None
        self._initialize_data_sources()
    
    def _initialize_data_sources(self):
        """Initialize available data sources."""
        try:
            # Try to initialize SPX data downloader
            from quantlab.data.data_downloader import SPXDataDownloader
            self._spx_downloader = SPXDataDownloader()
            self.logger.info("SPX data downloader initialized successfully")
        except Exception as e:
            self.logger.warning(f"SPX data downloader not available: {e}")
            self._spx_downloader = None
    
    @log_function_call
    def get_stock_data(self, tickers: List[str], start_date: datetime, end_date: datetime,
                      use_cache: bool = True) -> Tuple[List[str], Dict[str, pd.DataFrame]]:
        """
        Get stock data using the existing yfinance-based approach.
        
        Args:
            tickers: List of stock tickers
            start_date: Start date for data
            end_date: End date for data
            use_cache: Whether to use cached data
            
        Returns:
            Tuple of (valid_tickers, ticker_dataframes)
        """
        cache_file = self.data_dir / "stock_dataset.obj"
        
        if use_cache and self.cache_enabled and cache_file.exists():
            try:
                self.logger.info("Loading cached stock data")
                cached_tickers, cached_dfs = load_pickle(str(cache_file))
                
                # Check if cached data covers the requested period
                if self._is_cache_valid(cached_dfs, start_date, end_date):
                    # Filter to requested tickers
                    valid_tickers = [t for t in tickers if t in cached_tickers]
                    filtered_dfs = {t: cached_dfs[t] for t in valid_tickers}
                    self.logger.info(f"Using cached data for {len(valid_tickers)} tickers")
                    return valid_tickers, filtered_dfs
            except Exception as e:
                self.logger.warning(f"Failed to load cached data: {e}")
        
        # Fetch fresh data using existing methods
        self.logger.info(f"Fetching fresh stock data for {len(tickers)} tickers")
        from main import get_sp500_tickers, get_histories
        
        # If no specific tickers provided, get S&P 500
        if not tickers:
            tickers = get_sp500_tickers()
        
        # Fetch data
        starts = [start_date] * len(tickers)
        ends = [end_date] * len(tickers)
        valid_tickers, dfs = get_histories(tickers, starts, ends, granularity="1d")
        
        # Convert to dictionary
        ticker_dfs = {ticker: df for ticker, df in zip(valid_tickers, dfs)}
        
        # Cache the results
        if self.cache_enabled:
            try:
                save_pickle(str(cache_file), (valid_tickers, ticker_dfs))
                self.logger.info(f"Cached stock data for {len(valid_tickers)} tickers")
            except Exception as e:
                self.logger.warning(f"Failed to cache data: {e}")
        
        return valid_tickers, ticker_dfs
    
    @log_function_call
    def get_spx_options_data(self, start_date: datetime, end_date: datetime,
                           download_mode: str = "incremental",
                           calculate_greeks: bool = True) -> Optional[pd.DataFrame]:
        """
        Get SPX options data using the integrated data downloader.
        
        Args:
            start_date: Start date for data
            end_date: End date for data
            download_mode: "full" or "incremental"
            calculate_greeks: Whether to calculate Greeks
            
        Returns:
            DataFrame with SPX options data or None if unavailable
        """
        if not self._spx_downloader:
            self.logger.error("SPX data downloader not available")
            return None
        
        try:
            self.logger.info(f"Downloading SPX options data ({download_mode} mode)")
            
            if download_mode == "full":
                # Calculate months from date range
                months = max(1, int((end_date - start_date).days / 30))
                success = self._spx_downloader.full_download(months=months, 
                                                           calculate_greeks=calculate_greeks)
            else:
                success = self._spx_downloader.incremental_download()
            
            if success:
                # Load the downloaded data
                master_file = self._spx_downloader.file_paths['master_file']
                if os.path.exists(master_file):
                    try:
                        df = pd.read_parquet(master_file)
                        self.logger.info(f"Loaded {len(df)} SPX options records")
                        return df
                    except Exception as e:
                        self.logger.error(f"Failed to load SPX data: {e}")
                        # Try CSV fallback
                        csv_file = master_file.replace('.parquet', '.csv')
                        if os.path.exists(csv_file):
                            df = pd.read_csv(csv_file)
                            self.logger.info(f"Loaded {len(df)} SPX options records from CSV")
                            return df
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error downloading SPX options data: {e}")
            return None
    
    def get_combined_dataset(self, tickers: List[str], start_date: datetime, end_date: datetime,
                           include_spx_options: bool = False,
                           spx_download_mode: str = "incremental") -> Dict[str, Union[pd.DataFrame, Dict]]:
        """
        Get a combined dataset with both stock data and optionally SPX options data.
        
        Args:
            tickers: List of stock tickers
            start_date: Start date for data
            end_date: End date for data
            include_spx_options: Whether to include SPX options data
            spx_download_mode: SPX download mode ("full" or "incremental")
            
        Returns:
            Dictionary containing stock data and optionally SPX options data
        """
        result = {}
        
        # Get stock data
        self.logger.info("Fetching stock data...")
        valid_tickers, ticker_dfs = self.get_stock_data(tickers, start_date, end_date)
        result['stock_data'] = {
            'tickers': valid_tickers,
            'dataframes': ticker_dfs
        }
        
        # Get SPX options data if requested
        if include_spx_options:
            self.logger.info("Fetching SPX options data...")
            spx_data = self.get_spx_options_data(start_date, end_date, 
                                               download_mode=spx_download_mode)
            result['spx_options_data'] = spx_data
        
        return result
    
    def _is_cache_valid(self, cached_dfs: Dict[str, pd.DataFrame], 
                       start_date: datetime, end_date: datetime) -> bool:
        """
        Check if cached data covers the requested date range.
        
        Args:
            cached_dfs: Cached dataframes
            start_date: Requested start date
            end_date: Requested end date
            
        Returns:
            True if cache is valid for the date range
        """
        if not cached_dfs:
            return False
        
        try:
            # Check a sample of dataframes
            sample_df = next(iter(cached_dfs.values()))
            if sample_df.empty:
                return False
            
            # Get date range from cached data
            cache_start = sample_df.index.min()
            cache_end = sample_df.index.max()
            
            # Convert to timezone-aware if needed
            if cache_start.tz is None:
                cache_start = cache_start.tz_localize(pytz.utc)
            if cache_end.tz is None:
                cache_end = cache_end.tz_localize(pytz.utc)
            
            # Check if requested range is covered
            return cache_start <= start_date and cache_end >= end_date
            
        except Exception as e:
            self.logger.warning(f"Error validating cache: {e}")
            return False
    
    def get_data_summary(self) -> Dict[str, any]:
        """
        Get a summary of available data sources and cached data.
        
        Returns:
            Dictionary with data source information
        """
        summary = {
            'spx_downloader_available': self._spx_downloader is not None,
            'cache_enabled': self.cache_enabled,
            'data_directory': str(self.data_dir),
            'cached_files': []
        }
        
        # List cached files
        if self.data_dir.exists():
            for file_path in self.data_dir.glob("*.obj"):
                summary['cached_files'].append(file_path.name)
        
        # SPX data info
        if self._spx_downloader:
            try:
                metadata = self._spx_downloader.get_download_metadata()
                summary['spx_metadata'] = metadata
            except Exception as e:
                summary['spx_metadata'] = f"Error: {e}"
        
        return summary


def create_data_manager(data_dir: str = "data", cache_enabled: bool = True) -> DataIntegrationManager:
    """
    Factory function to create a data integration manager.
    
    Args:
        data_dir: Directory for storing data files
        cache_enabled: Whether to enable data caching
        
    Returns:
        Configured DataIntegrationManager instance
    """
    return DataIntegrationManager(data_dir=data_dir, cache_enabled=cache_enabled)
