import pytz
import mibian
import inspect
import numpy as np
import pandas as pd
from itertools import product
from datetime import datetime
from collections import defaultdict
from dateutil.relativedelta import relativedelta

from enum import Enum

import lzma
import pickle

def flatten(df):
    c = [f'{idx} {col}' for idx,col in product(df.index,df.columns)]
    return pd.DataFrame([df.values.ravel()],columns=c)

def get_delta(type,underlying_last,strike,dte,last):
    if type == "call":
        res = mibian.BS(
            [underlying_last,strike,0,dte],
            callPrice=last,
            volatility=mibian.BS(
                [underlying_last,strike,0,dte],callPrice=last
            ).impliedVolatility
        )
        delta=res.callDelta
    if type == "put":
        res = mibian.BS(
            [underlying_last,strike,0,dte],
            putPrice=last,
            volatility=mibian.BS(
                [underlying_last,strike,0,dte],putPrice=last
            ).impliedVolatility
        )
        delta=res.putDelta
    return delta

class REBALANCER(Enum):
    ON_DAYS_HELD=0
    ON_DAY_OF_WEEK=1
    DELTA_THRESHOLD=2

class HEDGE_TRIGGER(Enum):
    ON_DAY_OF_WEEK=1
    DELTA_THRESHOLD=2

class HEDGE_TARGET(Enum):
    INITIAL=0
    ZERO=1
    CONSTANT=2

class OPTION_SELECTOR():
    '''
    (ATM_REFERENCE_JUMP,2),(ATM_REFERENCE_JUMP,-2)
    '''
    class STRIKE(Enum):
        ATM_REFERENCE_JUMP=0 #90 95 (100) 105 110
        UNDERLYING_REFERENCE_DIST=1 #UNDERLYING = 100 > 110 0.10
        DELTA_NEAREST=2
        DELTA_MAX=3
        DELTA_MIN=4
        VOLUME=5
        OPEN_INTEREST=6

    class EXPIRY(Enum):
        NEAREST=10
        MAX=11
        MIN=12
        WEEKLY=13
        MONTHLY=14
        VOLUME=15
        OPEN_INTEREST=16

class AbstractImplementationException(Exception):
    pass

def nth_friday(dt,n):
    dt=pd.to_datetime(dt)
    day=dt.weekday()
    if day == 4 and n == 0:
        n+=1
    return dt + relativedelta(days=(4-day)%7) + relativedelta(days=7*n)

def third_friday(year,month):
    res = datetime(year,month,15,tzinfo=pytz.utc)
    w = res.weekday()
    if w!=4:
        res = res.replace(day=15+(4-w)%7)
    return res

from quantlab.performance import performance_measures
class OptAlpha():
    
    def __init__(self,instruments,trade_range,dfs,strategy,rebalance_triggers=[],
                hedge_triggers=[],hedge_target=(HEDGE_TARGET.ZERO,None),require_delta=None,hedger=None,portfolio_vol=0.15):
        self.instruments = instruments 
        self.trade_range = trade_range
        self.dfs = dfs
        self.strategy = strategy
        self.rebalance_triggers=rebalance_triggers
        self.hedge_triggers=hedge_triggers
        self.hedge_target=hedge_target
        self.hedger = hedger 
        assert not (self.hedger is not None and not (self.hedge_triggers is None or len(self.hedge_triggers) == 0))
        self.require_delta=require_delta | self.check_delta_triggers() if require_delta is not None else self.check_delta_triggers()
        self.portfolio_vol=portfolio_vol
        
    def check_delta_triggers(self):
        required=self.hedger is not None
        for trigger in self.rebalance_triggers:
            required |= (trigger[0] in [REBALANCER.DELTA_THRESHOLD])
        for trigger in self.hedge_triggers:
            required |= True
        for k,v in self.strategy["legs"].items():
            for _ in v["selector"]:
                selector=_[0]
                required |= (selector in [
                    OPTION_SELECTOR.STRIKE.DELTA_NEAREST,
                    OPTION_SELECTOR.STRIKE.DELTA_MAX,
                    OPTION_SELECTOR.STRIKE.DELTA_MIN,
                ])
        return required 
    
    def instantiate_variables(self):
        raise AbstractImplementationException()
    
    def load_buffer(self,load_from,min_buffer_len=100,min_hist_len=2): 
        raise AbstractImplementationException()

    def compute_signals(self,date,capital):
        raise AbstractImplementationException()
    
    def get_pnl(self,date,last):
        if date not in self.strat_buffer or last is None:
            return 0.0,0.0,0.0,0.0
        strategy_pos=last["strategy_pos"]
        other_underlying=last["other_underlying"]
        hedge_pos=last["hedge_pos"]
        strat_opt_pnl=np.sum(np.nan_to_num(self.strat_buffer[date]["opt_pnl"]*strategy_pos,posinf=0,neginf=0,nan=0))
        strat_underlying_pnl=np.sum(np.nan_to_num(self.strat_buffer[date]["underlying_pnl"]*strategy_pos*self.strategy["underlying"],posinf=0,neginf=0,nan=0))
        other_underlying_pnl=np.sum(np.nan_to_num(self.strat_buffer[date]["underlying_pnl"]*other_underlying,posinf=0,neginf=0,nan=0))
        hedge_pnl = np.sum(np.nan_to_num(self.strat_buffer[date]["underlying_pnl"]*hedge_pos,posinf=0,neginf=0,nan=0))
        return strat_opt_pnl,strat_underlying_pnl,other_underlying_pnl,hedge_pnl

    def option_selector(self,df,strategy):
        try:
            atm_strike = df.strike.values[np.argmin(np.abs(df.strike_dist))]
            strikes_listed = sorted(set(df.strike))
            for selector in strategy["selector"]:
                key,value = selector[0],selector[1]
                if inspect.isfunction(key):
                    df = key(df=df,strategy=strategy)
                if key == OPTION_SELECTOR.STRIKE.ATM_REFERENCE_JUMP:
                    df = df.loc[df.strike == strikes_listed[strikes_listed.index(atm_strike)+value]]
                if key == OPTION_SELECTOR.STRIKE.UNDERLYING_REFERENCE_DIST:
                    temp=df.underlying_last.values[0]*(1+value) 
                    df=df.loc[df.strike==df.strike.values[np.argmin(np.abs(temp-df.strike))]]
                if key == OPTION_SELECTOR.STRIKE.DELTA_NEAREST:
                    df=df.groupby("dte").apply(lambda x:x.loc[x.delta==x.delta.values[np.argmin(np.abs(value-x.delta))]])
                    df.index=df.index.droplevel("dte")
                if key == OPTION_SELECTOR.STRIKE.DELTA_MAX:
                    df=df.loc[df.delta <= value]
                if key == OPTION_SELECTOR.STRIKE.DELTA_MIN:
                    df=df.loc[df.delta >= value]
                if key == OPTION_SELECTOR.STRIKE.VOLUME:
                    df=df.groupby("dte").apply(lambda x:x.loc[x.volume==x.volume.max()])
                    df.index=df.index.droplevel("dte")
                if key == OPTION_SELECTOR.STRIKE.OPEN_INTEREST:
                    df=df.groupby("dte").apply(lambda x:x.loc[x.openinterest==x.openinterest.max()])
                    df.index=df.index.droplevel("dte")
            
                if key == OPTION_SELECTOR.EXPIRY.NEAREST:
                    df=df.loc[df.dte==df.dte.values[np.argmin(np.abs(value-df.dte))]]
                if key == OPTION_SELECTOR.EXPIRY.MAX:
                    df=df.loc[df.dte <= value]
                if key == OPTION_SELECTOR.EXPIRY.MIN:
                    df=df.loc[df.dte >= value]
                if key == OPTION_SELECTOR.EXPIRY.WEEKLY:
                    quoted=df.quotedate.values[0]
                    target=nth_friday(dt=quoted,n=value)
                    diff=(target-quoted).days
                    df=df.loc[np.logical_and(df.dte>=(diff-1),df.dte<=(diff+1))]
                if key == OPTION_SELECTOR.EXPIRY.MONTHLY:
                    quoted=pd.to_datetime(df.quotedate.values[0],utc=True)
                    target=quoted+relativedelta(months=value)
                    target=third_friday(year=target.year,month=target.month)
                    if quoted >= target and value == 0:
                        target=target+relativedelta(months=1)
                        target=third_friday(year=target.year,month=target.month)
                    diff=(target-quoted).days
                    df=df.loc[np.logical_and(df.dte>=(diff-1),df.dte<=(diff+1))]
                if key == OPTION_SELECTOR.EXPIRY.VOLUME:
                    df=df.groupby("strike").apply(lambda x:x.loc[x.volume==x.volume.max()])
                    df.index=df.index.droplevel("strike")
                if key == OPTION_SELECTOR.EXPIRY.OPEN_INTEREST:
                    df=df.groupby("strike").apply(lambda x:x.loc[x.openinterest==x.openinterest.max()])
                    df.index=df.index.droplevel("strike")
            delta=get_delta(
                type=df.type.values[0],
                underlying_last=df.underlying_last.values[0],
                strike=df.strike.values[0],
                dte=df.dte.values[0],
                last=df["last"].values[0]
            ) if "delta" not in df and self.require_delta else df.delta.values[0] if self.require_delta else np.nan
            return np.array([df.index.values[0],df.underlying_last.values[0],df["last"].values[0],delta])
        except:
            return np.nan
        
    def compute_buffer(self):
        assert all(
            col in df for col in [
                "optionroot","underlying","underlying_last","type",
                "quotedate","strike","last","volume","openinterest","dte"
            ] for df in self.data_buffer
        )
        optdata_buffer = [df.set_index("optionroot",drop=True) for df in self.data_buffer]

        def root_to_stats(df,root):
            try:
                contract=df.loc[root]
                delta=get_delta(
                    type=contract.type,
                    underlying_last=contract.underlying_last,
                    strike=contract.strike,
                    dte=contract.dte,
                    last=contract["last"]
                ) if "delta" not in contract and self.require_delta else contract.delta if self.require_delta else np.nan
                return np.array([root,contract.underlying_last,contract["last"],delta])
            except Exception as err:
                return np.nan
        
        def do_rebalance(df,date,prev_stats):
            date=pd.to_datetime(date)
            held_stats=prev_stats["contracts"].applymap(lambda root:root_to_stats(buffer_data,root))
            held_stats=held_stats.dropna()
            held_contracts=held_stats.applymap(lambda x:x[0])
            held_underlying=held_stats[list(held_stats)[0]].apply(lambda x:x[1]).astype(np.float64)
            held_prices=held_stats.applymap(lambda x:x[2]).astype(np.float64)
            held_delta=held_stats.applymap(lambda x:x[3]).astype(np.float64)
            
            held_contracts=pd.DataFrame(index=self.instruments).join(held_contracts)
            held_underlying=pd.DataFrame(index=self.instruments).join(held_underlying).iloc[:,0].values
            held_prices=pd.DataFrame(index=self.instruments).join(held_prices)
            held_delta=pd.DataFrame(index=self.instruments).join(held_delta)
        
            t1_value = held_prices.apply(lambda row:np.dot(row,prev_stats["multiplier"]),axis=1,raw=True).values
            t1_delta = held_delta.apply(lambda row:np.dot(row,prev_stats["multiplier"]),axis=1,raw=True).values
            t1_timestamp=prev_stats["opened"]
            t1_mask=(~np.isnan(t1_value)).astype(np.int32)
            opt_pnl=t1_value-prev_stats["optvalue"]
            rebalance_arr = np.isnan(t1_value).astype(np.int32)

            if not self.rebalance_triggers:
                rebalance_arr=np.ones(len(self.instruments))
            else:
                for rebalancer in self.rebalance_triggers:
                    k,v=rebalancer[0],rebalancer[1]
                    if inspect.isfunction(k):
                        rebalance_arr+=k(
                            date=date,
                            prev_stats=prev_stats,
                            held_contracts=held_contracts,
                            held_prices=held_prices,
                            held_underlying=held_underlying,
                            held_delta=held_delta,
                            t1_value=t1_value,
                            t1_delta=t1_delta,
                            t1_timestamp=t1_timestamp,
                            t1_mask=t1_mask,
                            dobj=self
                        ).astype(np.int32)
                    if k == REBALANCER.ON_DAY_OF_WEEK:
                        week_old=lambda diff:int(diff.days >= 7)
                        rebalance_arr+=np.vectorize(week_old)(date-t1_timestamp)
                        rebalance_arr+=np.array([int(date.weekday()==v)]*len(self.instruments))
                    if k == REBALANCER.ON_DAYS_HELD:
                        held_longer = lambda diff : int(diff.days > v)
                        rebalance_arr+=np.vectorize(held_longer)(date-t1_timestamp)
                    if k == REBALANCER.DELTA_THRESHOLD:
                        delta_dist=np.abs(t1_delta-prev_stats["delta_init"])
                        rebalance_arr+=(delta_dist>v).astype(np.int32)
                
            return held_underlying,opt_pnl,held_contracts,held_prices,t1_value,t1_delta,prev_stats["delta_init"],t1_timestamp,t1_mask,(rebalance_arr>0).astype(bool)
        
        def map_rebalancer(fro,to,rebalancer,dim):
            if dim == 1:
                return np.where(rebalancer,to,fro)
            if dim == 2:
                new=pd.DataFrame(np.where(rebalancer,to.T.values,fro.T.values)).T
                new.index=to.index
                new.columns=list(to)
                return new

        for i,buffer_data,buffer_idx in zip(range(len(optdata_buffer)),optdata_buffer,self.data_buffer_idx):
            print(buffer_idx)
            if buffer_idx in self.strat_buffer: continue
            buffer_data["strike_dist"] = np.abs(buffer_data.underlying_last - buffer_data.strike)
            dgroup = buffer_data.groupby("type")
            temp = pd.DataFrame(index=self.instruments)
            multiplier = []
            for leg,config in self.strategy["legs"].items():
                try:
                    inst_legs=dgroup.get_group(config["type"]).groupby("underlying").apply(lambda df:self.option_selector(df,config))
                except:
                    inst_legs = pd.Series(index=self.instruments)
                temp[leg] = inst_legs
                multiplier.append(config["contracts"])

            multiplier=np.array(multiplier)
            temp = temp.dropna()
            contracts=temp.applymap(lambda x:x[0])
            underlying=temp[list(temp)[0]].apply(lambda x:x[1]).astype(np.float64)
            prices=temp.applymap(lambda x:x[2]).astype(np.float64)
            delta=temp.applymap(lambda x:x[3]).astype(np.float64)

            contracts=pd.DataFrame(index=self.instruments).join(contracts)
            underlying=pd.DataFrame(index=self.instruments).join(underlying).iloc[:,0].values
            prices=pd.DataFrame(index=self.instruments).join(prices)
            delta=pd.DataFrame(index=self.instruments).join(delta)

            t0_value = prices.apply(lambda row:np.dot(row,multiplier),axis=1,raw=True).values
            t0_delta = delta.apply(lambda row:np.dot(row,multiplier),axis=1,raw=True).values
            t0_timestamp=np.array([buffer_idx]*len(self.instruments))
            t0_mask=(~np.isnan(t0_value)).astype(np.int32)
            
            if len(self.strat_buffer) == 0:
                self.strat_buffer[buffer_idx].update({
                    "contracts":contracts,
                    "prices":prices,
                    "multiplier":multiplier,
                    "optvalue":t0_value,
                    "optdelta":t0_delta,
                    "underlying":underlying,
                    "mask":t0_mask,

                    "opened":t0_timestamp,
                    "delta_init":t0_delta,
                    "rebalancer":np.ones(len(self.instruments))
                })
            else:
                prev_stats=self.strat_buffer[self.data_buffer_idx[i-1]]
                held_underlying,opt_pnl,held_contracts,held_prices,\
                t1_value,t1_delta,delta_init,t1_timestamp,t1_mask,rebalance_arr=do_rebalance(
                    df=buffer_data,
                    date=buffer_idx,
                    prev_stats=prev_stats
                )
             
                underlying=np.where(~np.isnan(underlying),underlying,held_underlying)
                contracts=map_rebalancer(fro=held_contracts,to=contracts,rebalancer=rebalance_arr,dim=2)
                prices=map_rebalancer(fro=held_prices,to=prices,rebalancer=rebalance_arr,dim=2)
                t0_value=map_rebalancer(fro=t1_value,to=t0_value,rebalancer=rebalance_arr,dim=1)
                t0_delta=map_rebalancer(fro=t1_delta,to=t0_delta,rebalancer=rebalance_arr,dim=1)
                t0_mask=map_rebalancer(fro=t1_mask,to=t0_mask,rebalancer=rebalance_arr,dim=1)
                delta_init=map_rebalancer(fro=delta_init,to=t0_delta,rebalancer=rebalance_arr,dim=1)
                t0_timestamp=map_rebalancer(fro=t1_timestamp,to=t0_timestamp,rebalancer=rebalance_arr,dim=1)
                self.strat_buffer[buffer_idx].update({
                    "contracts":contracts,
                    "prices":prices,
                    "multiplier":multiplier,
                    "optvalue":t0_value,
                    "optdelta":t0_delta,
                    "underlying":underlying,
                    "mask":t0_mask,

                    "opened":t0_timestamp,
                    "delta_init":delta_init,
                    "opt_pnl":opt_pnl,
                    "underlying_pnl":underlying-prev_stats["underlying"],
                    "rebalancer":rebalance_arr
                })
        return 
    
    def hedge_portfolio(self,capital,date,positions,prev):
        hedge_stats=self.strat_buffer[date]
        if self.hedger is not None:
            positions["hedge_pos"]=self.hedger(
                capital=capital,
                date=date,
                positions=positions,
                prev=prev,
                hedge_stats=hedge_stats,
                dobj=self
            )
        def do_hedge():
            hedge_mask=np.zeros(len(self.instruments))
            if not self.hedge_triggers:
                return hedge_mask
            for trigger in self.hedge_triggers:
                k,v=trigger[0],trigger[1]
                if k == HEDGE_TRIGGER.ON_DAY_OF_WEEK:
                    hedge_mask+=np.array([int(date.weekday()==v)]*len(self.instruments))
                if k == HEDGE_TRIGGER.DELTA_THRESHOLD:
                    position_delta=positions["strategy_pos"]*hedge_stats["optdelta"]
                    hedged_delta=prev["hedge_pos"] if prev is not None else np.zeros(len(self.instruments))
                    target_key,target_value=(self.hedge_target[0],self.hedge_target[1])
                    if target_key == HEDGE_TARGET.INITIAL:
                        initial_delta=positions["strategy_pos"]*hedge_stats["delta_init"]
                        delta_dist=np.abs((position_delta+hedged_delta-initial_delta)/positions["strategy_pos"])
                        hedge_mask+=(delta_dist > v).astype(np.int32)
                    if target_key == HEDGE_TARGET.ZERO or target_key == HEDGE_TARGET.CONSTANT:
                        target_value = 0 if target_key == HEDGE_TARGET.ZERO else target_value*positions["strategy_pos"]
                        delta_dist=np.abs((position_delta+hedged_delta-target_value)/positions["strategy_pos"])
                        hedge_mask+=(delta_dist > v).astype(np.int32)
            return (np.nan_to_num(hedge_mask,posinf=0,neginf=0,nan=0)>0).astype(np.int32)
        
        def hedge_dist():
            target_key,target_value=(self.hedge_target[0],self.hedge_target[1])
            position_delta=positions["strategy_pos"]*hedge_stats["optdelta"]
            hedged_delta=prev["hedge_pos"] if prev is not None else np.zeros(len(self.instruments))

            if target_key == HEDGE_TARGET.INITIAL:
                target_delta=positions["strategy_pos"]*hedge_stats["delta_init"]
            if target_key == HEDGE_TARGET.ZERO or target_key == HEDGE_TARGET.CONSTANT:
                target_delta = 0 if target_key == HEDGE_TARGET.ZERO else target_value*positions["strategy_pos"]
            return hedged_delta, position_delta + hedged_delta - target_delta
        
        hedge_held,to_hedge=hedge_dist()
        positions["hedge_pos"]=hedge_held+do_hedge()*to_hedge*-1
        return positions

    def compute_metas(self):
        raise AbstractImplementationException()
    
    def get_strat_scaler(self, target_vol, ewmas, ewstrats):
        ann_realized_vol = np.sqrt(ewmas[-1] * 253)
        return target_vol / ann_realized_vol * ewstrats[-1]
    
    def get_perf_stats(self,plot=False):
        assert self.portfolio_df is not None
        df=self.portfolio_df
        r=df.capital_ret
        return performance_measures(r=r.loc[r!=0],plot=plot)
    
    async def run_simulation(self):
        trade_start = self.trade_range[0]
        trade_end = self.trade_range[1]
        trade_range = pd.date_range(
            start=datetime(trade_start.year,trade_start.month,trade_start.day),
            end=datetime(trade_end.year,trade_end.month,trade_end.day),
            freq="D",
            tz=pytz.utc
        )

        portfolio_df = pd.DataFrame(index=trade_range).reset_index().rename(columns={"index":"datetime"})
        self.data_buffer = []
        self.data_buffer_idx = []
        self.strat_buffer = defaultdict(dict)
        
        self.instantiate_variables()
        last_positions=None

        capitals=[10000]
        portfolio_pnls,strat_option_pnls,strat_underlying_pnls,other_pnls,hedge_pnls=[0],[0],[0],[0],[0]
        capital_rets=[0.0]
        strategy_poss,other_poss,hedge_poss=[],[],[]

        underlying_prices,position_deltas,hedge_deltas,dollar_delta_positions,\
            dollar_delta_hedges,dollar_net_positions,\
            dollar_agg_positions={},{},{},{},{},{},{}
        contracts_log,prices_log,optvalue_log,optdelta_log,timestamp_log={},{},{},{},{}

        self.compute_metas(trade_range=trade_range)

        ewmas, ewstrats = [0.01], [1]
        strat_scalars = []

        for i in portfolio_df.index:
            date = portfolio_df.at[i,"datetime"]
            strat_scalar = 2

            if not any(dt >= date for dt in self.data_buffer_idx):
                self.load_buffer(load_from=date,test_end=trade_end,min_buffer_len=180,min_hist_len=2)
                self.compute_buffer()

            if i != 0:
                strat_scalar = self.get_strat_scaler(
                    target_vol=self.portfolio_vol,
                    ewmas=ewmas,
                    ewstrats=ewstrats
                )
                                
                strat_opt_pnl,strat_underlying_pnl,other_pnl,hedge_pnl = self.get_pnl(date=date,last=last_positions)
                portfolio_pnl=strat_opt_pnl+strat_underlying_pnl+other_pnl+hedge_pnl
                capital_ret=portfolio_pnl/capitals[-1]
                capital_rets.append(capital_ret)
                capitals.append(capitals[-1]+portfolio_pnl)
                portfolio_pnls.append(portfolio_pnl)
                strat_option_pnls.append(strat_opt_pnl)
                strat_underlying_pnls.append(strat_underlying_pnl)
                other_pnls.append(other_pnl)
                hedge_pnls.append(hedge_pnl)

                ewmas.append(0.06 * (capital_ret**2) + 0.94 * ewmas[-1] if capital_ret != 0 else ewmas[-1])
                ewstrats.append(0.06 * strat_scalar + 0.94 * ewstrats[-1] if capital_ret != 0 else ewstrats[-1])

            strat_scalars.append(strat_scalar)
            if date not in self.data_buffer_idx:
                positions=last_positions
            else:
                positions=self.compute_signals(date=date,capital=capitals[-1])
                positions["strategy_pos"]=np.nan_to_num(positions["strategy_pos"],posinf=0,neginf=0,nan=0)
                positions["other_underlying"]=np.nan_to_num(positions["other_underlying"],posinf=0,neginf=0,nan=0)
                positions["strategy_pos"]*=strat_scalar
                positions["other_underlying"]*=strat_scalar
                positions=self.hedge_portfolio(capital=capitals[-1],date=date,positions=positions,prev=last_positions)
                positions["hedge_pos"]=np.nan_to_num(positions["hedge_pos"],posinf=0,neginf=0,nan=0)

                hedge_stats=self.strat_buffer[date]
                position_delta=positions["strategy_pos"]*hedge_stats["optdelta"]
                hedge_delta=positions["hedge_pos"]
                dollar_delta_position=position_delta*hedge_stats["underlying"]
                dollar_delta_hedge=hedge_delta*hedge_stats["underlying"]
                dollar_net_position=dollar_delta_position+dollar_delta_hedge
                dollar_agg_position=np.sum(np.nan_to_num(dollar_net_position,posinf=0,neginf=0,nan=0))

                position_deltas[date]=position_delta
                hedge_deltas[date]=hedge_delta
                dollar_delta_positions[date]=dollar_delta_position
                dollar_delta_hedges[date]=dollar_delta_hedge
                dollar_net_positions[date]=dollar_net_position
                dollar_agg_positions[date]=dollar_agg_position
                underlying_prices[date]=hedge_stats["underlying"]

                contracts_log[date]=flatten(hedge_stats["contracts"]).values[0]
                prices_log[date]=flatten(hedge_stats["prices"]).values[0]
                optvalue_log[date]=hedge_stats["optvalue"]
                optdelta_log[date]=hedge_stats["optdelta"]
                timestamp_log[date]=hedge_stats["opened"]
            
            last_positions = positions
            strategy_poss.append(last_positions["strategy_pos"] if last_positions else np.zeros(len(self.instruments)))
            other_poss.append(last_positions["other_underlying"] if last_positions else np.zeros(len(self.instruments)))
            hedge_poss.append(last_positions["hedge_pos"] if last_positions else np.zeros(len(self.instruments)))

        strategy_df=pd.DataFrame(data=strategy_poss,index=trade_range,columns=self.instruments)
        other_df=pd.DataFrame(data=other_poss,index=trade_range,columns=[inst + " other" for inst in self.instruments])
        hedge_df=pd.DataFrame(data=hedge_poss,index=trade_range,columns=[inst + " hedge" for inst in self.instruments])

        capital_ret_ser=pd.Series(data=capital_rets,index=trade_range,name="capital_ret")
        capital_ser=pd.Series(data=capitals,index=trade_range,name="capital")
        pnl_ser=pd.Series(data=portfolio_pnls,index=trade_range,name="pnl")
        opt_pnl_ser=pd.Series(data=strat_option_pnls,index=trade_range,name="stratopt pnl")
        und_pnl_ser=pd.Series(data=strat_underlying_pnls,index=trade_range,name="stratund pnl")
        other_pnl_ser=pd.Series(data=other_pnls,index=trade_range,name="other pnl")
        hedge_pnl_ser=pd.Series(data=hedge_pnls,index=trade_range,name="hedge pnl")

        contracts_df=pd.DataFrame(index=trade_range).join(
            pd.DataFrame.from_dict(data=contracts_log,columns=flatten(hedge_stats["contracts"]).columns,orient="index")
        )
        prices_df=pd.DataFrame(index=trade_range).join(
            pd.DataFrame.from_dict(data=prices_log,columns=[col + " prices" for col in flatten(hedge_stats["prices"]).columns],orient="index")
        )
        optvalue_df=pd.DataFrame(index=trade_range).join(pd.DataFrame.from_dict(data=optvalue_log,orient="index"))
        optvalue_df.columns=[inst + " optvalue" for inst in self.instruments]
        optdelta_df=pd.DataFrame.from_dict(data=optdelta_log,orient="index")
        optdelta_df.columns=[inst + " optdelta" for inst in self.instruments]
        timestamp_df=pd.DataFrame.from_dict(data=timestamp_log,orient="index")
        timestamp_df.columns=[inst + " selection" for inst in self.instruments]
        
        underlying_df=pd.DataFrame(index=trade_range).join(pd.DataFrame.from_dict(data=underlying_prices,orient="index"))
        underlying_df.columns=[inst + " underlying" for inst in self.instruments]

        positions_delta_df=pd.DataFrame(index=trade_range).join(pd.DataFrame.from_dict(data=position_deltas,orient="index"))
        positions_delta_df.columns=[inst + " posdelta" for inst in self.instruments]

        hedge_delta_df=pd.DataFrame(index=trade_range).join(pd.DataFrame.from_dict(data=hedge_deltas,orient="index"))
        hedge_delta_df.columns=[inst + " hedgedelta" for inst in self.instruments]

        dollar_delta_pos_df=pd.DataFrame(index=trade_range).join(pd.DataFrame.from_dict(data=dollar_delta_positions,orient="index"))
        dollar_delta_pos_df.columns=[inst + " dollar_pos_delta" for inst in self.instruments]

        dollar_delta_hedge_df=pd.DataFrame(index=trade_range).join(pd.DataFrame.from_dict(data=dollar_delta_hedges,orient="index"))
        dollar_delta_hedge_df.columns=[inst + " dollar_hedge_delta" for inst in self.instruments]

        dollar_net_df=pd.DataFrame(index=trade_range).join(pd.DataFrame.from_dict(data=dollar_net_positions,orient="index"))
        dollar_net_df.columns=[inst + " dollar_net" for inst in self.instruments]

        dollar_agg_ser=pd.DataFrame(index=trade_range).join(pd.DataFrame.from_dict(data=dollar_agg_positions,orient="index"))
        dollar_agg_ser.columns=["dollar_agg"]
        dollar_agg_ser=dollar_agg_ser.iloc[:,0]
        
        self.portfolio_df = pd.concat([
            contracts_df,
            prices_df,
            optvalue_df,
            optdelta_df,
            timestamp_df,
            underlying_df,
            strategy_df,
            other_df,
            hedge_df,
            positions_delta_df,
            hedge_delta_df,
            dollar_delta_pos_df,
            dollar_delta_hedge_df,
            dollar_net_df,
            dollar_agg_ser,
            und_pnl_ser,
            other_pnl_ser,
            opt_pnl_ser,
            hedge_pnl_ser,
            pnl_ser,
            capital_ret_ser,
            capital_ser
        ],axis=1)
        
        self.logdfs = {
            "dollar_delta_pos_df":dollar_delta_pos_df,
            "dollar_delta_hedge_df":dollar_delta_hedge_df,
            "dollar_net_df":dollar_net_df,
            "dollar_agg_ser":dollar_agg_ser
        }

        return self.portfolio_df