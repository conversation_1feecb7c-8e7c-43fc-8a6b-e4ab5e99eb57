#!/usr/bin/env python3
"""
Iron Condor Strategy Implementation

This module implements the Iron Condor options strategy using the extended OptAlpha framework.
An Iron Condor consists of:
- Sell OTM Put (short put)
- Buy Further OTM Put (long put protection)
- Sell OTM Call (short call)  
- Buy Further OTM Call (long call protection)
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging

from .base_strategy import BaseMultiLegStrategy, StrategyLeg
from quantlab.optalpha import OPTION_SELECTOR, REBALANCER

logger = logging.getLogger(__name__)

class IronCondorStrategy(BaseMultiLegStrategy):
    """
    Iron Condor options strategy implementation.
    
    The Iron Condor is a neutral strategy that profits from low volatility
    and time decay. It has limited profit potential and limited risk.
    """
    
    def __init__(self, wing_width: float = 50, put_otm_distance: float = 0.05,
                 call_otm_distance: float = 0.05, target_dte: int = 30,
                 profit_target: float = 0.5, stop_loss: float = 2.0,
                 time_stop: int = 7, **kwargs):
        """
        Initialize Iron Condor strategy.
        
        Args:
            wing_width: Distance between short and long strikes (in points)
            put_otm_distance: Distance of short put from ATM (as percentage)
            call_otm_distance: Distance of short call from ATM (as percentage)
            target_dte: Target days to expiration for entry
            profit_target: Profit target as multiple of credit received
            stop_loss: Stop loss as multiple of credit received
            time_stop: Days before expiration to close position
            **kwargs: Additional arguments passed to BaseMultiLegStrategy
        """
        self.wing_width = wing_width
        self.put_otm_distance = put_otm_distance
        self.call_otm_distance = call_otm_distance
        self.target_dte = target_dte

        # Define the four legs of the Iron Condor
        legs = self.define_strategy_legs()
        
        super().__init__(
            strategy_name="Iron Condor",
            legs=legs,
            wing_width=wing_width,
            target_dte=target_dte,
            profit_target=profit_target,
            stop_loss=stop_loss,
            time_stop=time_stop,
            **kwargs
        )
        
        logger.info(f"Initialized Iron Condor: wing_width={wing_width}, "
                   f"put_otm={put_otm_distance}, call_otm={call_otm_distance}")
    
    def define_strategy_legs(self) -> List[StrategyLeg]:
        """Define the four legs of the Iron Condor strategy."""
        
        return [
            # Short Put (sell OTM put)
            StrategyLeg(
                side='sell',
                option_type='put',
                strike_selector='otm',
                quantity=1,
                strike_offset=self.wing_width * self.put_otm_distance,
                dte_target=self.target_dte
            ),
            
            # Long Put (buy further OTM put for protection)
            StrategyLeg(
                side='buy',
                option_type='put',
                strike_selector='otm',
                quantity=1,
                strike_offset=self.wing_width * self.put_otm_distance + self.wing_width,
                dte_target=self.target_dte
            ),
            
            # Short Call (sell OTM call)
            StrategyLeg(
                side='sell',
                option_type='call',
                strike_selector='otm',
                quantity=1,
                strike_offset=self.wing_width * self.call_otm_distance,
                dte_target=self.target_dte
            ),
            
            # Long Call (buy further OTM call for protection)
            StrategyLeg(
                side='buy',
                option_type='call',
                strike_selector='otm',
                quantity=1,
                strike_offset=self.wing_width * self.call_otm_distance + self.wing_width,
                dte_target=self.target_dte
            )
        ]
    
    def find_iron_condor_strikes(self, underlying_price: float, 
                                options_data: pd.DataFrame) -> Dict[str, float]:
        """
        Find optimal strikes for Iron Condor based on current underlying price.
        
        Args:
            underlying_price: Current price of underlying
            options_data: Available options data
            
        Returns:
            Dictionary with strike prices for each leg
        """
        strikes = {}
        
        # Calculate target strikes
        short_put_target = underlying_price * (1 - self.put_otm_distance)
        long_put_target = short_put_target - self.wing_width
        short_call_target = underlying_price * (1 + self.call_otm_distance)
        long_call_target = short_call_target + self.wing_width
        
        # Find closest available strikes
        puts = options_data[options_data['type'] == 'put']
        calls = options_data[options_data['type'] == 'call']
        
        if not puts.empty:
            # Short put strike
            put_diffs = abs(puts['strike'] - short_put_target)
            strikes['short_put'] = puts.loc[put_diffs.idxmin(), 'strike']
            
            # Long put strike (must be below short put)
            long_put_candidates = puts[puts['strike'] < strikes['short_put']]
            if not long_put_candidates.empty:
                long_put_diffs = abs(long_put_candidates['strike'] - long_put_target)
                strikes['long_put'] = long_put_candidates.loc[long_put_diffs.idxmin(), 'strike']
        
        if not calls.empty:
            # Short call strike
            call_diffs = abs(calls['strike'] - short_call_target)
            strikes['short_call'] = calls.loc[call_diffs.idxmin(), 'strike']
            
            # Long call strike (must be above short call)
            long_call_candidates = calls[calls['strike'] > strikes['short_call']]
            if not long_call_candidates.empty:
                long_call_diffs = abs(long_call_candidates['strike'] - long_call_target)
                strikes['long_call'] = long_call_candidates.loc[long_call_diffs.idxmin(), 'strike']
        
        return strikes
    
    def calculate_iron_condor_pnl(self, expiration_price: float, 
                                 strikes: Dict[str, float], 
                                 premiums: Dict[str, float]) -> Dict[str, float]:
        """
        Calculate Iron Condor P&L at expiration.
        
        Args:
            expiration_price: Price of underlying at expiration
            strikes: Dictionary of strike prices
            premiums: Dictionary of option premiums
            
        Returns:
            Dictionary with P&L breakdown
        """
        # Net credit received
        credit = (premiums.get('short_put', 0) + premiums.get('short_call', 0) - 
                 premiums.get('long_put', 0) - premiums.get('long_call', 0))
        
        # Put spread P&L
        if expiration_price <= strikes.get('long_put', 0):
            # Max loss on put spread
            put_pnl = -(strikes.get('short_put', 0) - strikes.get('long_put', 0))
        elif expiration_price >= strikes.get('short_put', 0):
            # No loss on put spread
            put_pnl = 0
        else:
            # Partial loss on put spread
            put_pnl = -(strikes.get('short_put', 0) - expiration_price)
        
        # Call spread P&L
        if expiration_price >= strikes.get('long_call', float('inf')):
            # Max loss on call spread
            call_pnl = -(strikes.get('long_call', 0) - strikes.get('short_call', 0))
        elif expiration_price <= strikes.get('short_call', 0):
            # No loss on call spread
            call_pnl = 0
        else:
            # Partial loss on call spread
            call_pnl = -(expiration_price - strikes.get('short_call', 0))
        
        # Total P&L
        total_pnl = credit + put_pnl + call_pnl
        
        return {
            'credit_received': credit,
            'put_pnl': put_pnl,
            'call_pnl': call_pnl,
            'total_pnl': total_pnl,
            'max_profit': credit,
            'max_loss': credit - max(
                strikes.get('short_put', 0) - strikes.get('long_put', 0),
                strikes.get('long_call', 0) - strikes.get('short_call', 0)
            )
        }
    
    def get_breakeven_points(self, strikes: Dict[str, float], 
                           credit: float) -> Tuple[float, float]:
        """
        Calculate breakeven points for Iron Condor.
        
        Args:
            strikes: Dictionary of strike prices
            credit: Net credit received
            
        Returns:
            Tuple of (lower_breakeven, upper_breakeven)
        """
        lower_breakeven = strikes.get('short_put', 0) - credit
        upper_breakeven = strikes.get('short_call', 0) + credit
        
        return lower_breakeven, upper_breakeven
    
    def compute_signals(self, date, capital):
        """
        Compute Iron Condor trading signals.
        
        This method determines when to enter/exit Iron Condor positions
        based on market conditions and strategy rules.
        """
        # Get current market data
        if not hasattr(self, 'data_buffer') or not self.data_buffer:
            return {'strategy_pos': np.zeros(len(self.instruments)),
                   'other_underlying': np.zeros(len(self.instruments))}
        
        # Find current data
        current_data = None
        for i, buffer_date in enumerate(self.data_buffer_idx):
            if buffer_date.date() == date.date():
                current_data = self.data_buffer[i]
                break
        
        if current_data is None or current_data.empty:
            return {'strategy_pos': np.zeros(len(self.instruments)),
                   'other_underlying': np.zeros(len(self.instruments))}
        
        # Get underlying price
        underlying_price = current_data['underlying_last'].iloc[0]
        
        # Check if we should enter a new position
        if self.current_position is None:
            # Entry logic
            contracts = self.find_option_contracts(date, underlying_price, current_data)
            
            if len(contracts) == 4:  # All four legs found
                # Calculate entry credit
                entry_credit = 0
                for leg_name, contract in contracts.items():
                    leg_idx = int(leg_name.split('_')[1])
                    leg = self.legs[leg_idx]
                    if leg.side == 'sell':
                        entry_credit += contract['last']
                    else:
                        entry_credit -= contract['last']
                
                # Enter position if credit is positive
                if entry_credit > 0:
                    self.current_position = contracts
                    self.entry_date = date
                    self.entry_credit = entry_credit
                    self.position_dte = current_data['dte'].iloc[0]
                    
                    logger.info(f"Entered Iron Condor on {date.date()} for ${entry_credit:.2f} credit")
                    
                    # Return position signal (simplified)
                    return {'strategy_pos': np.ones(len(self.instruments)),
                           'other_underlying': np.zeros(len(self.instruments))}
        
        else:
            # Position management logic
            current_pnl = self.calculate_strategy_pnl({
                leg_name: contract['last'] 
                for leg_name, contract in self.current_position.items()
            })
            
            days_held = (date - self.entry_date).days
            current_dte = current_data['dte'].iloc[0]
            
            should_close, reason = self.should_close_position(current_pnl, days_held, current_dte)
            
            if should_close:
                logger.info(f"Closing Iron Condor on {date.date()}: {reason}, P&L: ${current_pnl:.2f}")
                self.current_position = None
                self.entry_date = None
                self.entry_credit = 0
                
                return {'strategy_pos': np.zeros(len(self.instruments)),
                       'other_underlying': np.zeros(len(self.instruments))}
            
            # Hold position
            return {'strategy_pos': np.ones(len(self.instruments)),
                   'other_underlying': np.zeros(len(self.instruments))}
        
        # Default: no position
        return {'strategy_pos': np.zeros(len(self.instruments)),
               'other_underlying': np.zeros(len(self.instruments))}
    
    def get_strategy_metrics(self) -> Dict[str, float]:
        """Get Iron Condor specific performance metrics."""
        
        base_metrics = super().get_strategy_summary()
        
        # Add Iron Condor specific metrics
        iron_condor_metrics = {
            'wing_width': self.wing_width,
            'put_otm_distance': self.put_otm_distance,
            'call_otm_distance': self.call_otm_distance,
            'avg_credit_received': np.mean([pos.get('entry_credit', 0) 
                                          for pos in self.strategy_positions]) if self.strategy_positions else 0,
            'win_rate': len([p for p in self.strategy_pnl_history if p > 0]) / len(self.strategy_pnl_history) 
                       if self.strategy_pnl_history else 0,
            'avg_winner': np.mean([p for p in self.strategy_pnl_history if p > 0]) 
                         if any(p > 0 for p in self.strategy_pnl_history) else 0,
            'avg_loser': np.mean([p for p in self.strategy_pnl_history if p < 0])
                        if any(p < 0 for p in self.strategy_pnl_history) else 0
        }
        
        return {**base_metrics, **iron_condor_metrics}
