"""
Multi-Leg Options Strategies Module

This module extends the OptAlpha framework to support complex multi-leg options strategies
including iron condors, butterflies, calendars, spreads, and other advanced strategies.
"""

from .base_strategy import BaseMultiLegStrategy
from .iron_condor import IronCondorStrategy
from .butterfly import ButterflyStrategy
from .calendar import CalendarStrategy
from .spread import SpreadStrategy

__all__ = [
    'BaseMultiLegStrategy',
    'IronCondorStrategy', 
    'ButterflyStrategy',
    'CalendarStrategy',
    'SpreadStrategy'
]
