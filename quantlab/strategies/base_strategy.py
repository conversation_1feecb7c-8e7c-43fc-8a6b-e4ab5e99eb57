#!/usr/bin/env python3
"""
Base Multi-Leg Strategy Class

This module provides the base class for implementing complex multi-leg options strategies
that extend the OptAlpha framework.
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Union
from abc import ABC, abstractmethod
import logging

from quantlab.optalpha import OptAlpha, OPTION_SELECTOR, REBALANCER, HEDGE_TRIGGER, HEDGE_TARGET

logger = logging.getLogger(__name__)

class StrategyLeg:
    """Represents a single leg of a multi-leg options strategy."""
    
    def __init__(self, side: str, option_type: str, strike_selector: str, 
                 quantity: int = 1, strike_offset: float = 0, dte_target: int = 30):
        """
        Initialize a strategy leg.
        
        Args:
            side: 'buy' or 'sell'
            option_type: 'call' or 'put'
            strike_selector: Method for selecting strike ('atm', 'otm', 'itm', 'delta', 'fixed')
            quantity: Number of contracts (positive for long, negative for short)
            strike_offset: Offset from reference strike (in points or percentage)
            dte_target: Target days to expiration
        """
        self.side = side
        self.option_type = option_type
        self.strike_selector = strike_selector
        self.quantity = quantity if side == 'buy' else -abs(quantity)
        self.strike_offset = strike_offset
        self.dte_target = dte_target
        
        # Runtime attributes (set during execution)
        self.selected_strike = None
        self.selected_contract = None
        self.entry_price = None
        self.current_price = None
        self.delta = None
        self.gamma = None
        self.theta = None
        self.vega = None

class BaseMultiLegStrategy(OptAlpha):
    """
    Base class for multi-leg options strategies.
    
    Extends OptAlpha to support complex strategies with multiple option legs,
    sophisticated position management, and strategy-specific risk controls.
    """
    
    def __init__(self, strategy_name: str, legs: List[StrategyLeg], 
                 wing_width: float = 50, target_dte: int = 30,
                 profit_target: float = 0.5, stop_loss: float = 2.0,
                 time_stop: int = 7, **kwargs):
        """
        Initialize the multi-leg strategy.
        
        Args:
            strategy_name: Name of the strategy
            legs: List of StrategyLeg objects defining the strategy
            wing_width: Width between strikes (for spreads)
            target_dte: Target days to expiration for entry
            profit_target: Profit target as multiple of credit received
            stop_loss: Stop loss as multiple of credit received
            time_stop: Days before expiration to close position
            **kwargs: Additional arguments passed to OptAlpha
        """
        self.strategy_name = strategy_name
        self.legs = legs
        self.wing_width = wing_width
        self.target_dte = target_dte
        self.profit_target = profit_target
        self.stop_loss = stop_loss
        self.time_stop = time_stop
        
        # Strategy state
        self.current_position = None
        self.entry_date = None
        self.entry_credit = 0.0
        self.position_dte = None
        
        # Initialize parent OptAlpha class
        super().__init__(**kwargs)
        
        logger.info(f"Initialized {strategy_name} with {len(legs)} legs")
    
    @abstractmethod
    def define_strategy_legs(self) -> List[StrategyLeg]:
        """Define the legs of the strategy. Must be implemented by subclasses."""
        pass
    
    def find_option_contracts(self, date: datetime, underlying_price: float, 
                            options_data: pd.DataFrame) -> Dict[str, pd.Series]:
        """
        Find the appropriate option contracts for each leg of the strategy.
        
        Args:
            date: Trading date
            underlying_price: Current underlying price
            options_data: Available options data for the date
            
        Returns:
            Dictionary mapping leg names to selected option contracts
        """
        selected_contracts = {}
        
        for i, leg in enumerate(self.legs):
            leg_name = f"leg_{i}_{leg.side}_{leg.option_type}"
            
            # Filter options by type
            leg_options = options_data[
                (options_data['type'] == leg.option_type) &
                (options_data['dte'] >= leg.dte_target - 2) &
                (options_data['dte'] <= leg.dte_target + 2)
            ].copy()
            
            if leg_options.empty:
                logger.warning(f"No {leg.option_type} options found for {leg_name}")
                continue
            
            # Select strike based on selector method
            selected_contract = self._select_strike(leg, leg_options, underlying_price)
            
            if selected_contract is not None:
                selected_contracts[leg_name] = selected_contract
                leg.selected_contract = selected_contract
                leg.selected_strike = selected_contract['strike']
                leg.entry_price = selected_contract['last']
        
        return selected_contracts
    
    def _select_strike(self, leg: StrategyLeg, options: pd.DataFrame, 
                      underlying_price: float) -> Optional[pd.Series]:
        """Select the appropriate strike for a strategy leg."""
        
        if options.empty:
            return None
        
        if leg.strike_selector == 'atm':
            # Find closest to ATM
            options['strike_diff'] = abs(options['strike'] - underlying_price)
            return options.loc[options['strike_diff'].idxmin()]
        
        elif leg.strike_selector == 'otm':
            if leg.option_type == 'call':
                # OTM calls: strikes above current price
                otm_options = options[options['strike'] > underlying_price]
                if not otm_options.empty:
                    target_strike = underlying_price + leg.strike_offset
                    otm_options['strike_diff'] = abs(otm_options['strike'] - target_strike)
                    return otm_options.loc[otm_options['strike_diff'].idxmin()]
            else:
                # OTM puts: strikes below current price
                otm_options = options[options['strike'] < underlying_price]
                if not otm_options.empty:
                    target_strike = underlying_price - leg.strike_offset
                    otm_options['strike_diff'] = abs(otm_options['strike'] - target_strike)
                    return otm_options.loc[otm_options['strike_diff'].idxmin()]
        
        elif leg.strike_selector == 'delta':
            # Select by delta (requires delta calculation)
            if 'delta' in options.columns:
                target_delta = leg.strike_offset  # Use offset as target delta
                options['delta_diff'] = abs(options['delta'] - target_delta)
                return options.loc[options['delta_diff'].idxmin()]
        
        elif leg.strike_selector == 'fixed':
            # Fixed strike
            target_strike = leg.strike_offset
            options['strike_diff'] = abs(options['strike'] - target_strike)
            return options.loc[options['strike_diff'].idxmin()]
        
        # Default: return closest to ATM
        options['strike_diff'] = abs(options['strike'] - underlying_price)
        return options.loc[options['strike_diff'].idxmin()]
    
    def calculate_strategy_pnl(self, current_prices: Dict[str, float]) -> float:
        """
        Calculate the current P&L of the multi-leg strategy.
        
        Args:
            current_prices: Dictionary of current option prices by leg name
            
        Returns:
            Total strategy P&L
        """
        total_pnl = 0.0
        
        for i, leg in enumerate(self.legs):
            leg_name = f"leg_{i}_{leg.side}_{leg.option_type}"
            
            if leg_name in current_prices and leg.entry_price is not None:
                current_price = current_prices[leg_name]
                
                if leg.side == 'sell':
                    # For sold options: credit received - current price
                    leg_pnl = (leg.entry_price - current_price) * abs(leg.quantity) * 100
                else:
                    # For bought options: current price - debit paid
                    leg_pnl = (current_price - leg.entry_price) * abs(leg.quantity) * 100
                
                total_pnl += leg_pnl
                leg.current_price = current_price
        
        return total_pnl
    
    def should_close_position(self, current_pnl: float, days_held: int, 
                            current_dte: int) -> Tuple[bool, str]:
        """
        Determine if the position should be closed based on strategy rules.
        
        Args:
            current_pnl: Current position P&L
            days_held: Number of days position has been held
            current_dte: Current days to expiration
            
        Returns:
            Tuple of (should_close, reason)
        """
        # Profit target
        if self.entry_credit > 0 and current_pnl >= self.entry_credit * self.profit_target:
            return True, "profit_target"
        
        # Stop loss
        if self.entry_credit > 0 and current_pnl <= -self.entry_credit * self.stop_loss:
            return True, "stop_loss"
        
        # Time stop
        if current_dte <= self.time_stop:
            return True, "time_stop"
        
        return False, "hold"
    
    def get_strategy_greeks(self) -> Dict[str, float]:
        """Calculate aggregate Greeks for the strategy."""
        
        total_delta = 0.0
        total_gamma = 0.0
        total_theta = 0.0
        total_vega = 0.0
        
        for leg in self.legs:
            if all(getattr(leg, greek) is not None for greek in ['delta', 'gamma', 'theta', 'vega']):
                total_delta += leg.delta * leg.quantity
                total_gamma += leg.gamma * leg.quantity
                total_theta += leg.theta * leg.quantity
                total_vega += leg.vega * leg.quantity
        
        return {
            'delta': total_delta,
            'gamma': total_gamma,
            'theta': total_theta,
            'vega': total_vega
        }
    
    def get_strategy_summary(self) -> Dict[str, any]:
        """Get a summary of the current strategy state."""
        
        return {
            'strategy_name': self.strategy_name,
            'num_legs': len(self.legs),
            'entry_date': self.entry_date,
            'entry_credit': self.entry_credit,
            'position_dte': self.position_dte,
            'target_dte': self.target_dte,
            'profit_target': self.profit_target,
            'stop_loss': self.stop_loss,
            'time_stop': self.time_stop,
            'legs': [
                {
                    'side': leg.side,
                    'type': leg.option_type,
                    'quantity': leg.quantity,
                    'strike': leg.selected_strike,
                    'entry_price': leg.entry_price,
                    'current_price': leg.current_price
                }
                for leg in self.legs
            ]
        }
    
    # Override OptAlpha methods to implement multi-leg logic
    def compute_signals(self, date, capital):
        """Compute trading signals for multi-leg strategy."""
        # This will be implemented by specific strategy subclasses
        return super().compute_signals(date, capital)
    
    def instantiate_variables(self):
        """Initialize strategy-specific variables."""
        super().instantiate_variables()
        
        # Initialize multi-leg specific variables
        self.strategy_positions = []
        self.strategy_pnl_history = []
        self.leg_performance = {f"leg_{i}": [] for i in range(len(self.legs))}
    
    def load_buffer(self, load_from, test_end=None, min_buffer_len=100, min_hist_len=2):
        """Load data buffer with options chain reconstruction."""
        # Call parent method
        super().load_buffer(load_from, test_end, min_buffer_len, min_hist_len)
        
        # Additional processing for options data
        self._process_options_chain()
    
    def _process_options_chain(self):
        """Process loaded data to reconstruct options chains."""
        # This method will process the loaded data to create
        # structured options chains for strategy execution
        logger.info("Processing options chains for multi-leg strategy")
        
        # Implementation will depend on data structure
        # For now, we'll prepare the data for strategy execution
        pass
