#!/usr/bin/env python3
"""
Butterfly Strategy Implementation

This module implements the Butterfly options strategy using the extended OptAlpha framework.
A Butterfly consists of:
- Buy 1 ITM option
- Sell 2 ATM options  
- Buy 1 OTM option

Can be implemented with calls (Long Call Butterfly) or puts (Long Put Butterfly).
"""

import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Tuple
import logging

from .base_strategy import BaseMultiLegStrategy, StrategyLeg
from quantlab.optalpha import OPTION_SELECTOR, REBALANCER

logger = logging.getLogger(__name__)

class ButterflyStrategy(BaseMultiLegStrategy):
    """
    Butterfly options strategy implementation.
    
    The Butterfly is a neutral strategy that profits when the underlying
    stays close to the middle strike at expiration.
    """
    
    def __init__(self, butterfly_type: str = 'call', wing_width: float = 50,
                 target_dte: int = 30, profit_target: float = 0.75,
                 stop_loss: float = 2.0, time_stop: int = 7, **kwargs):
        """
        Initialize Butterfly strategy.
        
        Args:
            butterfly_type: 'call' or 'put' butterfly
            wing_width: Distance between strikes (in points)
            target_dte: Target days to expiration for entry
            profit_target: Profit target as multiple of debit paid
            stop_loss: Stop loss as multiple of debit paid
            time_stop: Days before expiration to close position
            **kwargs: Additional arguments passed to BaseMultiLegStrategy
        """
        self.butterfly_type = butterfly_type
        self.wing_width = wing_width
        self.target_dte = target_dte

        # Define the three legs of the Butterfly
        legs = self.define_strategy_legs()
        
        super().__init__(
            strategy_name=f"{butterfly_type.title()} Butterfly",
            legs=legs,
            wing_width=wing_width,
            target_dte=target_dte,
            profit_target=profit_target,
            stop_loss=stop_loss,
            time_stop=time_stop,
            **kwargs
        )
        
        logger.info(f"Initialized {butterfly_type.title()} Butterfly: wing_width={wing_width}")
    
    def define_strategy_legs(self) -> List[StrategyLeg]:
        """Define the three legs of the Butterfly strategy."""
        
        return [
            # Long ITM option (lower strike)
            StrategyLeg(
                side='buy',
                option_type=self.butterfly_type,
                strike_selector='fixed',
                quantity=1,
                strike_offset=-self.wing_width,  # ITM by wing_width
                dte_target=self.target_dte
            ),
            
            # Short 2 ATM options (middle strike)
            StrategyLeg(
                side='sell',
                option_type=self.butterfly_type,
                strike_selector='atm',
                quantity=2,
                strike_offset=0,  # ATM
                dte_target=self.target_dte
            ),
            
            # Long OTM option (higher strike)
            StrategyLeg(
                side='buy',
                option_type=self.butterfly_type,
                strike_selector='fixed',
                quantity=1,
                strike_offset=self.wing_width,  # OTM by wing_width
                dte_target=self.target_dte
            )
        ]
    
    def find_butterfly_strikes(self, underlying_price: float, 
                              options_data: pd.DataFrame) -> Dict[str, float]:
        """
        Find optimal strikes for Butterfly based on current underlying price.
        
        Args:
            underlying_price: Current price of underlying
            options_data: Available options data
            
        Returns:
            Dictionary with strike prices for each leg
        """
        strikes = {}
        
        # Filter for the correct option type
        options = options_data[options_data['type'] == self.butterfly_type]
        
        if options.empty:
            return strikes
        
        # Find ATM strike (middle)
        atm_diffs = abs(options['strike'] - underlying_price)
        middle_strike = options.loc[atm_diffs.idxmin(), 'strike']
        strikes['middle'] = middle_strike
        
        # Find lower strike (ITM)
        lower_target = middle_strike - self.wing_width
        lower_candidates = options[options['strike'] <= middle_strike]
        if not lower_candidates.empty:
            lower_diffs = abs(lower_candidates['strike'] - lower_target)
            strikes['lower'] = lower_candidates.loc[lower_diffs.idxmin(), 'strike']
        
        # Find upper strike (OTM)
        upper_target = middle_strike + self.wing_width
        upper_candidates = options[options['strike'] >= middle_strike]
        if not upper_candidates.empty:
            upper_diffs = abs(upper_candidates['strike'] - upper_target)
            strikes['upper'] = upper_candidates.loc[upper_diffs.idxmin(), 'strike']
        
        return strikes
    
    def calculate_butterfly_pnl(self, expiration_price: float,
                               strikes: Dict[str, float],
                               premiums: Dict[str, float]) -> Dict[str, float]:
        """
        Calculate Butterfly P&L at expiration.
        
        Args:
            expiration_price: Price of underlying at expiration
            strikes: Dictionary of strike prices
            premiums: Dictionary of option premiums
            
        Returns:
            Dictionary with P&L breakdown
        """
        # Net debit paid
        debit = (premiums.get('lower', 0) + premiums.get('upper', 0) - 
                2 * premiums.get('middle', 0))
        
        lower_strike = strikes.get('lower', 0)
        middle_strike = strikes.get('middle', 0)
        upper_strike = strikes.get('upper', 0)
        
        # Calculate intrinsic value at expiration
        if self.butterfly_type == 'call':
            lower_value = max(0, expiration_price - lower_strike)
            middle_value = max(0, expiration_price - middle_strike)
            upper_value = max(0, expiration_price - upper_strike)
        else:  # put butterfly
            lower_value = max(0, lower_strike - expiration_price)
            middle_value = max(0, middle_strike - expiration_price)
            upper_value = max(0, upper_strike - expiration_price)
        
        # P&L calculation: Long 1 lower + Long 1 upper - Short 2 middle - Debit paid
        expiration_value = lower_value + upper_value - 2 * middle_value
        total_pnl = expiration_value - debit
        
        # Maximum profit occurs when underlying equals middle strike
        max_profit = abs(middle_strike - lower_strike) - debit
        
        # Maximum loss is the debit paid
        max_loss = -debit
        
        return {
            'debit_paid': debit,
            'expiration_value': expiration_value,
            'total_pnl': total_pnl,
            'max_profit': max_profit,
            'max_loss': max_loss,
            'breakeven_lower': lower_strike + debit if self.butterfly_type == 'call' else lower_strike - debit,
            'breakeven_upper': upper_strike - debit if self.butterfly_type == 'call' else upper_strike + debit
        }
    
    def _select_strike(self, leg: StrategyLeg, options: pd.DataFrame, 
                      underlying_price: float) -> Optional[pd.Series]:
        """Override strike selection for butterfly-specific logic."""
        
        if options.empty:
            return None
        
        if leg.strike_selector == 'atm':
            # Find closest to ATM for middle strike
            options['strike_diff'] = abs(options['strike'] - underlying_price)
            return options.loc[options['strike_diff'].idxmin()]
        
        elif leg.strike_selector == 'fixed':
            # For butterfly, use ATM + offset
            target_strike = underlying_price + leg.strike_offset
            options['strike_diff'] = abs(options['strike'] - target_strike)
            return options.loc[options['strike_diff'].idxmin()]
        
        # Default to parent method
        return super()._select_strike(leg, options, underlying_price)
    
    def compute_signals(self, date, capital):
        """
        Compute Butterfly trading signals.
        
        This method determines when to enter/exit Butterfly positions
        based on market conditions and strategy rules.
        """
        # Get current market data
        if not hasattr(self, 'data_buffer') or not self.data_buffer:
            return {'strategy_pos': np.zeros(len(self.instruments)),
                   'other_underlying': np.zeros(len(self.instruments))}
        
        # Find current data
        current_data = None
        for i, buffer_date in enumerate(self.data_buffer_idx):
            if buffer_date.date() == date.date():
                current_data = self.data_buffer[i]
                break
        
        if current_data is None or current_data.empty:
            return {'strategy_pos': np.zeros(len(self.instruments)),
                   'other_underlying': np.zeros(len(self.instruments))}
        
        # Get underlying price
        underlying_price = current_data['underlying_last'].iloc[0]
        
        # Check if we should enter a new position
        if self.current_position is None:
            # Entry logic
            contracts = self.find_option_contracts(date, underlying_price, current_data)
            
            if len(contracts) == 3:  # All three legs found
                # Calculate entry debit
                entry_debit = 0
                for leg_name, contract in contracts.items():
                    leg_idx = int(leg_name.split('_')[1])
                    leg = self.legs[leg_idx]
                    if leg.side == 'buy':
                        entry_debit += contract['last'] * abs(leg.quantity)
                    else:
                        entry_debit -= contract['last'] * abs(leg.quantity)
                
                # Enter position if debit is reasonable
                if entry_debit > 0 and entry_debit < self.wing_width * 0.8:
                    self.current_position = contracts
                    self.entry_date = date
                    self.entry_credit = -entry_debit  # Negative for debit strategies
                    self.position_dte = current_data['dte'].iloc[0]
                    
                    logger.info(f"Entered {self.strategy_name} on {date.date()} for ${entry_debit:.2f} debit")
                    
                    return {'strategy_pos': np.ones(len(self.instruments)),
                           'other_underlying': np.zeros(len(self.instruments))}
        
        else:
            # Position management logic
            current_pnl = self.calculate_strategy_pnl({
                leg_name: contract['last'] 
                for leg_name, contract in self.current_position.items()
            })
            
            days_held = (date - self.entry_date).days
            current_dte = current_data['dte'].iloc[0]
            
            should_close, reason = self.should_close_position(current_pnl, days_held, current_dte)
            
            if should_close:
                logger.info(f"Closing {self.strategy_name} on {date.date()}: {reason}, P&L: ${current_pnl:.2f}")
                self.current_position = None
                self.entry_date = None
                self.entry_credit = 0
                
                return {'strategy_pos': np.zeros(len(self.instruments)),
                       'other_underlying': np.zeros(len(self.instruments))}
            
            # Hold position
            return {'strategy_pos': np.ones(len(self.instruments)),
                   'other_underlying': np.zeros(len(self.instruments))}
        
        # Default: no position
        return {'strategy_pos': np.zeros(len(self.instruments)),
               'other_underlying': np.zeros(len(self.instruments))}
    
    def get_strategy_metrics(self) -> Dict[str, float]:
        """Get Butterfly specific performance metrics."""
        
        base_metrics = super().get_strategy_summary()
        
        # Add Butterfly specific metrics
        butterfly_metrics = {
            'butterfly_type': self.butterfly_type,
            'wing_width': self.wing_width,
            'avg_debit_paid': abs(np.mean([pos.get('entry_credit', 0) 
                                         for pos in self.strategy_positions])) if self.strategy_positions else 0,
            'win_rate': len([p for p in self.strategy_pnl_history if p > 0]) / len(self.strategy_pnl_history) 
                       if self.strategy_pnl_history else 0,
            'avg_winner': np.mean([p for p in self.strategy_pnl_history if p > 0]) 
                         if any(p > 0 for p in self.strategy_pnl_history) else 0,
            'avg_loser': np.mean([p for p in self.strategy_pnl_history if p < 0])
                        if any(p < 0 for p in self.strategy_pnl_history) else 0,
            'max_theoretical_profit': self.wing_width  # Theoretical max profit
        }
        
        return {**base_metrics, **butterfly_metrics}
