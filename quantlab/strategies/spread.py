#!/usr/bin/env python3
"""
Vertical Spread Strategy Implementation

This module implements Vertical Spread options strategies using the extended OptAlpha framework.
Vertical Spreads include:
- Bull Call Spread: Buy lower strike call, sell higher strike call
- Bear Call Spread: Sell lower strike call, buy higher strike call  
- Bull Put Spread: Sell higher strike put, buy lower strike put
- Bear Put Spread: Buy higher strike put, sell lower strike put
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging

from .base_strategy import BaseMultiLegStrategy, StrategyLeg
from quantlab.optalpha import OPTION_SELECTOR, REBALANCER

logger = logging.getLogger(__name__)

class SpreadStrategy(BaseMultiLegStrategy):
    """
    Vertical Spread options strategy implementation.
    
    Vertical spreads are directional strategies with limited risk and reward.
    """
    
    def __init__(self, spread_type: str = 'bull_call', strike_width: float = 50,
                 otm_distance: float = 0.02, target_dte: int = 30,
                 profit_target: float = 0.5, stop_loss: float = 2.0,
                 time_stop: int = 7, **kwargs):
        """
        Initialize Vertical Spread strategy.
        
        Args:
            spread_type: 'bull_call', 'bear_call', 'bull_put', 'bear_put'
            strike_width: Distance between strikes (in points)
            otm_distance: Distance from ATM for entry (as percentage)
            target_dte: Target days to expiration for entry
            profit_target: Profit target as multiple of credit/debit
            stop_loss: Stop loss as multiple of credit/debit
            time_stop: Days before expiration to close position
            **kwargs: Additional arguments passed to BaseMultiLegStrategy
        """
        self.spread_type = spread_type
        self.strike_width = strike_width
        self.otm_distance = otm_distance
        self.target_dte = target_dte

        # Parse spread type
        self.direction, self.option_type = spread_type.split('_')
        self.is_credit_spread = (spread_type in ['bear_call', 'bull_put'])

        # Define the two legs of the spread
        legs = self.define_strategy_legs()
        
        super().__init__(
            strategy_name=spread_type.replace('_', ' ').title(),
            legs=legs,
            wing_width=strike_width,
            target_dte=target_dte,
            profit_target=profit_target,
            stop_loss=stop_loss,
            time_stop=time_stop,
            **kwargs
        )
        
        logger.info(f"Initialized {self.strategy_name}: width={strike_width}, otm={otm_distance}")
    
    def define_strategy_legs(self) -> List[StrategyLeg]:
        """Define the two legs of the vertical spread."""
        
        legs = []
        
        if self.spread_type == 'bull_call':
            # Bull Call: Buy lower strike call, sell higher strike call
            legs = [
                StrategyLeg(
                    side='buy',
                    option_type='call',
                    strike_selector='otm',
                    quantity=1,
                    strike_offset=self.strike_width * self.otm_distance,
                    dte_target=self.target_dte
                ),
                StrategyLeg(
                    side='sell',
                    option_type='call',
                    strike_selector='otm',
                    quantity=1,
                    strike_offset=self.strike_width * self.otm_distance + self.strike_width,
                    dte_target=self.target_dte
                )
            ]
        
        elif self.spread_type == 'bear_call':
            # Bear Call: Sell lower strike call, buy higher strike call
            legs = [
                StrategyLeg(
                    side='sell',
                    option_type='call',
                    strike_selector='otm',
                    quantity=1,
                    strike_offset=self.strike_width * self.otm_distance,
                    dte_target=self.target_dte
                ),
                StrategyLeg(
                    side='buy',
                    option_type='call',
                    strike_selector='otm',
                    quantity=1,
                    strike_offset=self.strike_width * self.otm_distance + self.strike_width,
                    dte_target=self.target_dte
                )
            ]
        
        elif self.spread_type == 'bull_put':
            # Bull Put: Sell higher strike put, buy lower strike put
            legs = [
                StrategyLeg(
                    side='sell',
                    option_type='put',
                    strike_selector='otm',
                    quantity=1,
                    strike_offset=self.strike_width * self.otm_distance,
                    dte_target=self.target_dte
                ),
                StrategyLeg(
                    side='buy',
                    option_type='put',
                    strike_selector='otm',
                    quantity=1,
                    strike_offset=self.strike_width * self.otm_distance + self.strike_width,
                    dte_target=self.target_dte
                )
            ]
        
        elif self.spread_type == 'bear_put':
            # Bear Put: Buy higher strike put, sell lower strike put
            legs = [
                StrategyLeg(
                    side='buy',
                    option_type='put',
                    strike_selector='otm',
                    quantity=1,
                    strike_offset=self.strike_width * self.otm_distance + self.strike_width,
                    dte_target=self.target_dte
                ),
                StrategyLeg(
                    side='sell',
                    option_type='put',
                    strike_selector='otm',
                    quantity=1,
                    strike_offset=self.strike_width * self.otm_distance,
                    dte_target=self.target_dte
                )
            ]
        
        return legs
    
    def find_spread_strikes(self, underlying_price: float,
                           options_data: pd.DataFrame) -> Dict[str, float]:
        """
        Find optimal strikes for vertical spread.
        
        Args:
            underlying_price: Current price of underlying
            options_data: Available options data
            
        Returns:
            Dictionary with strike prices for each leg
        """
        strikes = {}
        
        # Filter for the correct option type
        options = options_data[options_data['type'] == self.option_type]
        
        if options.empty:
            return strikes
        
        # Calculate target strikes based on spread type
        if self.spread_type in ['bull_call', 'bear_call']:
            # For call spreads, both strikes are above current price
            lower_target = underlying_price * (1 + self.otm_distance)
            upper_target = lower_target + self.strike_width
            
            # Find lower strike
            lower_candidates = options[options['strike'] >= underlying_price]
            if not lower_candidates.empty:
                lower_diffs = abs(lower_candidates['strike'] - lower_target)
                strikes['lower'] = lower_candidates.loc[lower_diffs.idxmin(), 'strike']
                
                # Find upper strike (must be above lower)
                upper_candidates = options[options['strike'] > strikes['lower']]
                if not upper_candidates.empty:
                    upper_diffs = abs(upper_candidates['strike'] - upper_target)
                    strikes['upper'] = upper_candidates.loc[upper_diffs.idxmin(), 'strike']
        
        elif self.spread_type in ['bull_put', 'bear_put']:
            # For put spreads, both strikes are below current price
            upper_target = underlying_price * (1 - self.otm_distance)
            lower_target = upper_target - self.strike_width
            
            # Find upper strike
            upper_candidates = options[options['strike'] <= underlying_price]
            if not upper_candidates.empty:
                upper_diffs = abs(upper_candidates['strike'] - upper_target)
                strikes['upper'] = upper_candidates.loc[upper_diffs.idxmin(), 'strike']
                
                # Find lower strike (must be below upper)
                lower_candidates = options[options['strike'] < strikes['upper']]
                if not lower_candidates.empty:
                    lower_diffs = abs(lower_candidates['strike'] - lower_target)
                    strikes['lower'] = lower_candidates.loc[lower_diffs.idxmin(), 'strike']
        
        return strikes
    
    def calculate_spread_pnl(self, expiration_price: float,
                            strikes: Dict[str, float],
                            premiums: Dict[str, float]) -> Dict[str, float]:
        """
        Calculate vertical spread P&L at expiration.
        
        Args:
            expiration_price: Price of underlying at expiration
            strikes: Dictionary of strike prices
            premiums: Dictionary of option premiums
            
        Returns:
            Dictionary with P&L breakdown
        """
        lower_strike = strikes.get('lower', 0)
        upper_strike = strikes.get('upper', 0)
        
        # Calculate net credit/debit
        if self.is_credit_spread:
            # Credit spreads: receive premium upfront
            if self.spread_type == 'bear_call':
                net_credit = premiums.get('lower', 0) - premiums.get('upper', 0)
            else:  # bull_put
                net_credit = premiums.get('upper', 0) - premiums.get('lower', 0)
        else:
            # Debit spreads: pay premium upfront
            if self.spread_type == 'bull_call':
                net_debit = premiums.get('lower', 0) - premiums.get('upper', 0)
            else:  # bear_put
                net_debit = premiums.get('upper', 0) - premiums.get('lower', 0)
        
        # Calculate intrinsic values at expiration
        if self.option_type == 'call':
            lower_value = max(0, expiration_price - lower_strike)
            upper_value = max(0, expiration_price - upper_strike)
        else:  # put
            lower_value = max(0, lower_strike - expiration_price)
            upper_value = max(0, upper_strike - expiration_price)
        
        # Calculate P&L based on spread type
        if self.spread_type == 'bull_call':
            # Long lower, short upper
            spread_value = lower_value - upper_value
            total_pnl = spread_value - net_debit
            max_profit = (upper_strike - lower_strike) - net_debit
            max_loss = -net_debit
        
        elif self.spread_type == 'bear_call':
            # Short lower, long upper
            spread_value = upper_value - lower_value
            total_pnl = net_credit + spread_value
            max_profit = net_credit
            max_loss = net_credit - (upper_strike - lower_strike)
        
        elif self.spread_type == 'bull_put':
            # Short upper, long lower
            spread_value = lower_value - upper_value
            total_pnl = net_credit + spread_value
            max_profit = net_credit
            max_loss = net_credit - (upper_strike - lower_strike)
        
        elif self.spread_type == 'bear_put':
            # Long upper, short lower
            spread_value = upper_value - lower_value
            total_pnl = spread_value - net_debit
            max_profit = (upper_strike - lower_strike) - net_debit
            max_loss = -net_debit
        
        return {
            'spread_value': spread_value,
            'total_pnl': total_pnl,
            'max_profit': max_profit,
            'max_loss': max_loss,
            'net_premium': net_credit if self.is_credit_spread else -net_debit,
            'breakeven': self._calculate_breakeven(strikes, 
                                                 net_credit if self.is_credit_spread else net_debit)
        }
    
    def _calculate_breakeven(self, strikes: Dict[str, float], net_premium: float) -> float:
        """Calculate breakeven point for the spread."""
        
        if self.spread_type == 'bull_call':
            return strikes.get('lower', 0) + net_premium
        elif self.spread_type == 'bear_call':
            return strikes.get('lower', 0) + net_premium
        elif self.spread_type == 'bull_put':
            return strikes.get('upper', 0) - net_premium
        elif self.spread_type == 'bear_put':
            return strikes.get('upper', 0) - net_premium
        
        return 0
    
    def compute_signals(self, date, capital):
        """
        Compute Vertical Spread trading signals.
        
        This method determines when to enter/exit spread positions
        based on market conditions and strategy rules.
        """
        # Get current market data
        if not hasattr(self, 'data_buffer') or not self.data_buffer:
            return {'strategy_pos': np.zeros(len(self.instruments)),
                   'other_underlying': np.zeros(len(self.instruments))}
        
        # Find current data
        current_data = None
        for i, buffer_date in enumerate(self.data_buffer_idx):
            if buffer_date.date() == date.date():
                current_data = self.data_buffer[i]
                break
        
        if current_data is None or current_data.empty:
            return {'strategy_pos': np.zeros(len(self.instruments)),
                   'other_underlying': np.zeros(len(self.instruments))}
        
        # Get underlying price
        underlying_price = current_data['underlying_last'].iloc[0]
        
        # Check if we should enter a new position
        if self.current_position is None:
            # Entry logic
            contracts = self.find_option_contracts(date, underlying_price, current_data)
            
            if len(contracts) == 2:  # Both legs found
                # Calculate entry credit/debit
                entry_premium = 0
                for leg_name, contract in contracts.items():
                    leg_idx = int(leg_name.split('_')[1])
                    leg = self.legs[leg_idx]
                    if leg.side == 'sell':
                        entry_premium += contract['last']
                    else:
                        entry_premium -= contract['last']
                
                # Enter position based on spread type
                should_enter = False
                if self.is_credit_spread and entry_premium > 0:
                    should_enter = True
                elif not self.is_credit_spread and entry_premium < 0:
                    should_enter = True
                
                if should_enter:
                    self.current_position = contracts
                    self.entry_date = date
                    self.entry_credit = entry_premium
                    self.position_dte = current_data['dte'].iloc[0]
                    
                    premium_type = "credit" if entry_premium > 0 else "debit"
                    logger.info(f"Entered {self.strategy_name} on {date.date()} "
                              f"for ${abs(entry_premium):.2f} {premium_type}")
                    
                    return {'strategy_pos': np.ones(len(self.instruments)),
                           'other_underlying': np.zeros(len(self.instruments))}
        
        else:
            # Position management logic
            current_pnl = self.calculate_strategy_pnl({
                leg_name: contract['last'] 
                for leg_name, contract in self.current_position.items()
            })
            
            days_held = (date - self.entry_date).days
            current_dte = current_data['dte'].iloc[0]
            
            should_close, reason = self.should_close_position(current_pnl, days_held, current_dte)
            
            if should_close:
                logger.info(f"Closing {self.strategy_name} on {date.date()}: {reason}, P&L: ${current_pnl:.2f}")
                self.current_position = None
                self.entry_date = None
                self.entry_credit = 0
                
                return {'strategy_pos': np.zeros(len(self.instruments)),
                       'other_underlying': np.zeros(len(self.instruments))}
            
            # Hold position
            return {'strategy_pos': np.ones(len(self.instruments)),
                   'other_underlying': np.zeros(len(self.instruments))}
        
        # Default: no position
        return {'strategy_pos': np.zeros(len(self.instruments)),
               'other_underlying': np.zeros(len(self.instruments))}
    
    def get_strategy_metrics(self) -> Dict[str, float]:
        """Get Vertical Spread specific performance metrics."""
        
        base_metrics = super().get_strategy_summary()
        
        # Add spread specific metrics
        spread_metrics = {
            'spread_type': self.spread_type,
            'is_credit_spread': self.is_credit_spread,
            'strike_width': self.strike_width,
            'otm_distance': self.otm_distance,
            'avg_premium': np.mean([abs(pos.get('entry_credit', 0)) 
                                  for pos in self.strategy_positions]) if self.strategy_positions else 0,
            'win_rate': len([p for p in self.strategy_pnl_history if p > 0]) / len(self.strategy_pnl_history) 
                       if self.strategy_pnl_history else 0,
            'avg_winner': np.mean([p for p in self.strategy_pnl_history if p > 0]) 
                         if any(p > 0 for p in self.strategy_pnl_history) else 0,
            'avg_loser': np.mean([p for p in self.strategy_pnl_history if p < 0])
                        if any(p < 0 for p in self.strategy_pnl_history) else 0,
            'max_theoretical_profit': self.strike_width if not self.is_credit_spread else None
        }
        
        return {**base_metrics, **spread_metrics}
