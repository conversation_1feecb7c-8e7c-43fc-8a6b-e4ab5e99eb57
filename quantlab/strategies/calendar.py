#!/usr/bin/env python3
"""
Calendar Spread Strategy Implementation

This module implements Calendar Spread options strategies using the extended OptAlpha framework.
A Calendar Spread consists of:
- Sell near-term option
- Buy longer-term option (same strike)

Can be implemented with calls or puts. Also supports Double Calendar spreads.
"""

import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Tuple
import logging

from .base_strategy import BaseMultiLegStrategy, StrategyLeg
from quantlab.optalpha import OPTION_SELECTOR, REBALANCER

logger = logging.getLogger(__name__)

class CalendarStrategy(BaseMultiLegStrategy):
    """
    Calendar Spread options strategy implementation.
    
    The Calendar Spread profits from time decay and low volatility,
    with maximum profit when underlying stays at the strike price.
    """
    
    def __init__(self, calendar_type: str = 'call', strike_selection: str = 'atm',
                 near_dte: int = 30, far_dte: int = 60, is_double: bool = False,
                 strike_width: float = 50, target_dte: int = 30,
                 profit_target: float = 1.5, stop_loss: float = 2.0,
                 time_stop: int = 7, **kwargs):
        """
        Initialize Calendar strategy.
        
        Args:
            calendar_type: 'call' or 'put' calendar
            strike_selection: 'atm', 'otm', or 'itm'
            near_dte: Target DTE for near-term option
            far_dte: Target DTE for far-term option
            is_double: Whether to create a double calendar (both calls and puts)
            strike_width: Width between strikes for double calendar
            target_dte: Target days to expiration for entry
            profit_target: Profit target as multiple of debit paid
            stop_loss: Stop loss as multiple of debit paid
            time_stop: Days before expiration to close position
            **kwargs: Additional arguments passed to BaseMultiLegStrategy
        """
        self.calendar_type = calendar_type
        self.strike_selection = strike_selection
        self.near_dte = near_dte
        self.far_dte = far_dte
        self.is_double = is_double
        self.strike_width = strike_width
        
        # Define the legs of the Calendar strategy
        legs = self.define_strategy_legs()
        
        strategy_name = f"Double Calendar" if is_double else f"{calendar_type.title()} Calendar"
        
        super().__init__(
            strategy_name=strategy_name,
            legs=legs,
            wing_width=strike_width,
            target_dte=target_dte,
            profit_target=profit_target,
            stop_loss=stop_loss,
            time_stop=time_stop,
            **kwargs
        )
        
        logger.info(f"Initialized {strategy_name}: near_dte={near_dte}, far_dte={far_dte}")
    
    def define_strategy_legs(self) -> List[StrategyLeg]:
        """Define the legs of the Calendar strategy."""
        
        legs = []
        
        # Determine strike offset based on selection
        if self.strike_selection == 'atm':
            strike_offset = 0
        elif self.strike_selection == 'otm':
            strike_offset = self.strike_width * 0.02  # 2% OTM
        elif self.strike_selection == 'itm':
            strike_offset = -self.strike_width * 0.02  # 2% ITM
        else:
            strike_offset = 0
        
        # Single Calendar legs
        legs.extend([
            # Sell near-term option
            StrategyLeg(
                side='sell',
                option_type=self.calendar_type,
                strike_selector=self.strike_selection,
                quantity=1,
                strike_offset=strike_offset,
                dte_target=self.near_dte
            ),
            
            # Buy far-term option (same strike)
            StrategyLeg(
                side='buy',
                option_type=self.calendar_type,
                strike_selector=self.strike_selection,
                quantity=1,
                strike_offset=strike_offset,
                dte_target=self.far_dte
            )
        ])
        
        # Add second calendar for double calendar
        if self.is_double:
            opposite_type = 'put' if self.calendar_type == 'call' else 'call'
            
            legs.extend([
                # Sell near-term opposite option
                StrategyLeg(
                    side='sell',
                    option_type=opposite_type,
                    strike_selector=self.strike_selection,
                    quantity=1,
                    strike_offset=strike_offset,
                    dte_target=self.near_dte
                ),
                
                # Buy far-term opposite option
                StrategyLeg(
                    side='buy',
                    option_type=opposite_type,
                    strike_selector=self.strike_selection,
                    quantity=1,
                    strike_offset=strike_offset,
                    dte_target=self.far_dte
                )
            ])
        
        return legs
    
    def find_calendar_contracts(self, underlying_price: float,
                               options_data: pd.DataFrame) -> Dict[str, pd.Series]:
        """
        Find appropriate contracts for calendar spread.
        
        Args:
            underlying_price: Current price of underlying
            options_data: Available options data
            
        Returns:
            Dictionary of selected contracts
        """
        contracts = {}
        
        # Determine target strike
        if self.strike_selection == 'atm':
            target_strike = underlying_price
        elif self.strike_selection == 'otm':
            if self.calendar_type == 'call':
                target_strike = underlying_price * 1.02  # 2% OTM for calls
            else:
                target_strike = underlying_price * 0.98  # 2% OTM for puts
        elif self.strike_selection == 'itm':
            if self.calendar_type == 'call':
                target_strike = underlying_price * 0.98  # 2% ITM for calls
            else:
                target_strike = underlying_price * 1.02  # 2% ITM for puts
        else:
            target_strike = underlying_price
        
        # Find contracts for each leg
        for i, leg in enumerate(self.legs):
            leg_name = f"leg_{i}_{leg.side}_{leg.option_type}"
            
            # Filter by option type and DTE
            leg_options = options_data[
                (options_data['type'] == leg.option_type) &
                (options_data['dte'] >= leg.dte_target - 5) &
                (options_data['dte'] <= leg.dte_target + 5)
            ].copy()
            
            if leg_options.empty:
                continue
            
            # Find closest strike to target
            leg_options['strike_diff'] = abs(leg_options['strike'] - target_strike)
            selected = leg_options.loc[leg_options['strike_diff'].idxmin()]
            
            contracts[leg_name] = selected
            leg.selected_contract = selected
            leg.selected_strike = selected['strike']
            leg.entry_price = selected['last']
        
        return contracts
    
    def calculate_calendar_pnl(self, current_prices: Dict[str, float],
                              time_decay_factor: float = 1.0) -> Dict[str, float]:
        """
        Calculate Calendar spread P&L considering time decay.
        
        Args:
            current_prices: Current option prices
            time_decay_factor: Factor representing time decay effect
            
        Returns:
            Dictionary with P&L breakdown
        """
        total_pnl = 0.0
        near_term_pnl = 0.0
        far_term_pnl = 0.0
        
        for i, leg in enumerate(self.legs):
            leg_name = f"leg_{i}_{leg.side}_{leg.option_type}"
            
            if leg_name in current_prices and leg.entry_price is not None:
                current_price = current_prices[leg_name]
                
                if leg.side == 'sell':
                    # For sold options: credit received - current price
                    leg_pnl = (leg.entry_price - current_price) * abs(leg.quantity) * 100
                else:
                    # For bought options: current price - debit paid
                    leg_pnl = (current_price - leg.entry_price) * abs(leg.quantity) * 100
                
                total_pnl += leg_pnl
                
                # Separate near and far term P&L
                if leg.dte_target == self.near_dte:
                    near_term_pnl += leg_pnl
                else:
                    far_term_pnl += leg_pnl
        
        return {
            'total_pnl': total_pnl,
            'near_term_pnl': near_term_pnl,
            'far_term_pnl': far_term_pnl,
            'time_decay_benefit': near_term_pnl * time_decay_factor
        }
    
    def compute_signals(self, date, capital):
        """
        Compute Calendar trading signals.
        
        This method determines when to enter/exit Calendar positions
        based on market conditions and strategy rules.
        """
        # Get current market data
        if not hasattr(self, 'data_buffer') or not self.data_buffer:
            return {'strategy_pos': np.zeros(len(self.instruments)),
                   'other_underlying': np.zeros(len(self.instruments))}
        
        # Find current data
        current_data = None
        for i, buffer_date in enumerate(self.data_buffer_idx):
            if buffer_date.date() == date.date():
                current_data = self.data_buffer[i]
                break
        
        if current_data is None or current_data.empty:
            return {'strategy_pos': np.zeros(len(self.instruments)),
                   'other_underlying': np.zeros(len(self.instruments))}
        
        # Get underlying price
        underlying_price = current_data['underlying_last'].iloc[0]
        
        # Check if we should enter a new position
        if self.current_position is None:
            # Entry logic
            contracts = self.find_calendar_contracts(underlying_price, current_data)
            
            expected_legs = 4 if self.is_double else 2
            if len(contracts) == expected_legs:
                # Calculate entry debit
                entry_debit = 0
                for leg_name, contract in contracts.items():
                    leg_idx = int(leg_name.split('_')[1])
                    leg = self.legs[leg_idx]
                    if leg.side == 'buy':
                        entry_debit += contract['last'] * abs(leg.quantity)
                    else:
                        entry_debit -= contract['last'] * abs(leg.quantity)
                
                # Enter position if debit is reasonable
                if entry_debit > 0:
                    self.current_position = contracts
                    self.entry_date = date
                    self.entry_credit = -entry_debit  # Negative for debit strategies
                    self.position_dte = current_data['dte'].iloc[0]
                    
                    logger.info(f"Entered {self.strategy_name} on {date.date()} for ${entry_debit:.2f} debit")
                    
                    return {'strategy_pos': np.ones(len(self.instruments)),
                           'other_underlying': np.zeros(len(self.instruments))}
        
        else:
            # Position management logic
            current_pnl = self.calculate_strategy_pnl({
                leg_name: contract['last'] 
                for leg_name, contract in self.current_position.items()
            })
            
            days_held = (date - self.entry_date).days
            current_dte = current_data['dte'].iloc[0]
            
            # Special calendar management: close when near-term option approaches expiration
            near_term_dte = min([leg.dte_target for leg in self.legs])
            if current_dte <= near_term_dte + 7:  # Close 7 days before near-term expiration
                logger.info(f"Closing {self.strategy_name} on {date.date()}: near expiration, P&L: ${current_pnl:.2f}")
                self.current_position = None
                self.entry_date = None
                self.entry_credit = 0
                
                return {'strategy_pos': np.zeros(len(self.instruments)),
                       'other_underlying': np.zeros(len(self.instruments))}
            
            should_close, reason = self.should_close_position(current_pnl, days_held, current_dte)
            
            if should_close:
                logger.info(f"Closing {self.strategy_name} on {date.date()}: {reason}, P&L: ${current_pnl:.2f}")
                self.current_position = None
                self.entry_date = None
                self.entry_credit = 0
                
                return {'strategy_pos': np.zeros(len(self.instruments)),
                       'other_underlying': np.zeros(len(self.instruments))}
            
            # Hold position
            return {'strategy_pos': np.ones(len(self.instruments)),
                   'other_underlying': np.zeros(len(self.instruments))}
        
        # Default: no position
        return {'strategy_pos': np.zeros(len(self.instruments)),
               'other_underlying': np.zeros(len(self.instruments))}
    
    def get_strategy_metrics(self) -> Dict[str, float]:
        """Get Calendar specific performance metrics."""
        
        base_metrics = super().get_strategy_summary()
        
        # Add Calendar specific metrics
        calendar_metrics = {
            'calendar_type': self.calendar_type,
            'is_double': self.is_double,
            'near_dte': self.near_dte,
            'far_dte': self.far_dte,
            'dte_spread': self.far_dte - self.near_dte,
            'avg_debit_paid': abs(np.mean([pos.get('entry_credit', 0) 
                                         for pos in self.strategy_positions])) if self.strategy_positions else 0,
            'win_rate': len([p for p in self.strategy_pnl_history if p > 0]) / len(self.strategy_pnl_history) 
                       if self.strategy_pnl_history else 0,
            'avg_winner': np.mean([p for p in self.strategy_pnl_history if p > 0]) 
                         if any(p > 0 for p in self.strategy_pnl_history) else 0,
            'avg_loser': np.mean([p for p in self.strategy_pnl_history if p < 0])
                        if any(p < 0 for p in self.strategy_pnl_history) else 0
        }
        
        return {**base_metrics, **calendar_metrics}
