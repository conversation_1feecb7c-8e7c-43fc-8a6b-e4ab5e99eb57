"""
Greeks Calculator for the SPX Options Trading System.

This module provides comprehensive Greeks calculation capabilities
including first, second, and third order Greeks with caching.
"""

from typing import Dict, List, Any, Optional
import pandas as pd
import numpy as np
from scipy.stats import norm
from scipy.optimize import brentq
import logging

logger = logging.getLogger(__name__)


class GreeksCalculator:
    """
    Comprehensive Greeks calculator with Black-Scholes implementation.
    
    Calculates first, second, and third order Greeks with caching
    and validation for options pricing and risk management.
    """
    
    def __init__(self):
        self.risk_free_rate = 0.05  # Default 5%
        self.dividend_yield = 0.02  # Default 2% for SPX

        # Initialize logger
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def analyze(self, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """
        Calculate Greeks for the provided options data.
        
        Args:
            data: Options data with required columns
            **kwargs: Additional parameters
            
        Returns:
            Dict containing calculated Greeks
        """
        try:
            # Calculate all Greeks
            greeks_data = self.calculate_greeks(data)
            
            # Calculate second-order Greeks
            second_order_data = self.calculate_second_order_greeks(greeks_data)
            
            # Analyze Greek trends if requested
            trends = {}
            if kwargs.get('analyze_trends', False):
                lookback_days = kwargs.get('lookback_days', 5)
                trends = self.analyze_greek_trends(second_order_data, lookback_days)
            
            return {
                'greeks_data': greeks_data,
                'second_order_data': second_order_data,
                'trends': trends,
                'calculation_summary': self._get_calculation_summary(greeks_data)
            }
            
        except Exception as e:
            self.logger.error(f"Error in Greeks analysis: {e}")
            raise
    
    def calculate_greeks(self, options_data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate first-order Greeks using Black-Scholes model.
        
        Args:
            options_data: DataFrame with options data
            
        Returns:
            DataFrame with calculated Greeks
        """
        try:
            data = options_data.copy()
            
            # Validate required columns
            required_columns = ['strike_price', 'spx_price', 'dte', 'implied_volatility', 'contract_type']
            missing_columns = [col for col in required_columns if col not in data.columns]
            
            if missing_columns:
                raise ValueError(f"Missing required columns for Greeks calculation: {missing_columns}")
            
            # Convert DTE to time to expiration in years
            data['time_to_expiry'] = data['dte'] / 365.0
            
            # Calculate d1 and d2 for Black-Scholes
            data['d1'] = self._calculate_d1(
                data['spx_price'], 
                data['strike_price'], 
                data['time_to_expiry'], 
                data['implied_volatility']
            )
            data['d2'] = data['d1'] - data['implied_volatility'] * np.sqrt(data['time_to_expiry'])
            
            # Calculate Greeks
            data['delta'] = self._calculate_delta(data)
            data['gamma'] = self._calculate_gamma(data)
            data['theta'] = self._calculate_theta(data)
            data['vega'] = self._calculate_vega(data)
            data['rho'] = self._calculate_rho(data)
            
            # Clean up intermediate columns
            data = data.drop(['d1', 'd2'], axis=1)
            
            self.logger.info(f"Calculated Greeks for {len(data):,} options")
            return data
            
        except Exception as e:
            self.logger.error(f"Error calculating Greeks: {e}")
            raise
    
    def calculate_second_order_greeks(self, options_data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate second-order Greeks (vanna, charm, etc.).
        
        Args:
            options_data: DataFrame with first-order Greeks
            
        Returns:
            DataFrame with second-order Greeks added
        """
        try:
            data = options_data.copy()
            
            # Validate required columns for second-order Greeks
            required_columns = ['strike_price', 'spx_price', 'dte', 'implied_volatility', 'delta', 'gamma']
            missing_columns = [col for col in required_columns if col not in data.columns]
            
            if missing_columns:
                self.logger.warning(f"Missing columns for second-order Greeks: {missing_columns}")
                return data
            
            # Recalculate d1 and d2 if needed
            if 'time_to_expiry' not in data.columns:
                data['time_to_expiry'] = data['dte'] / 365.0
            
            data['d1'] = self._calculate_d1(
                data['spx_price'], 
                data['strike_price'], 
                data['time_to_expiry'], 
                data['implied_volatility']
            )
            data['d2'] = data['d1'] - data['implied_volatility'] * np.sqrt(data['time_to_expiry'])
            
            # Calculate second-order Greeks
            data['vanna'] = self._calculate_vanna(data)
            data['charm'] = self._calculate_charm(data)
            data['vomma'] = self._calculate_vomma(data)
            data['speed'] = self._calculate_speed(data)
            
            # Clean up intermediate columns
            data = data.drop(['d1', 'd2'], axis=1, errors='ignore')
            
            self.logger.info(f"Calculated second-order Greeks for {len(data):,} options")
            return data
            
        except Exception as e:
            self.logger.error(f"Error calculating second-order Greeks: {e}")
            return options_data
    
    def analyze_greek_trends(self, options_data: pd.DataFrame, 
                           lookback_days: int = 5) -> Dict[str, Any]:
        """
        Analyze trends in Greeks over time.
        
        Args:
            options_data: DataFrame with Greeks data
            lookback_days: Number of days to look back for trend analysis
            
        Returns:
            Dict containing trend analysis
        """
        try:
            if 'date' not in options_data.columns:
                self.logger.warning("No date column found for trend analysis")
                return {}
            
            # Sort by date
            data = options_data.sort_values('date')
            
            # Get unique dates
            dates = sorted(data['date'].unique())
            
            if len(dates) < lookback_days:
                self.logger.warning(f"Insufficient data for trend analysis: {len(dates)} days < {lookback_days}")
                return {}
            
            # Analyze trends for each Greek
            greek_columns = ['delta', 'gamma', 'theta', 'vega', 'vanna', 'charm']
            available_greeks = [col for col in greek_columns if col in data.columns]
            
            trends = {}
            for greek in available_greeks:
                trends[greek] = self._analyze_single_greek_trend(data, greek, lookback_days)
            
            return {
                'trends': trends,
                'analysis_period': f"{dates[-lookback_days].strftime('%Y-%m-%d')} to {dates[-1].strftime('%Y-%m-%d')}",
                'lookback_days': lookback_days
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing Greek trends: {e}")
            return {}
    
    def _calculate_d1(self, spot: pd.Series, strike: pd.Series, 
                     time_to_expiry: pd.Series, volatility: pd.Series) -> pd.Series:
        """Calculate d1 parameter for Black-Scholes."""
        return (np.log(spot / strike) + 
                (self.risk_free_rate - self.dividend_yield + 0.5 * volatility**2) * time_to_expiry) / \
               (volatility * np.sqrt(time_to_expiry))
    
    def _calculate_delta(self, data: pd.DataFrame) -> pd.Series:
        """Calculate delta."""
        call_mask = data['contract_type'] == 'call'
        put_mask = data['contract_type'] == 'put'
        
        delta = pd.Series(index=data.index, dtype=float)
        
        # Call delta
        delta[call_mask] = np.exp(-self.dividend_yield * data.loc[call_mask, 'time_to_expiry']) * \
                          norm.cdf(data.loc[call_mask, 'd1'])
        
        # Put delta
        delta[put_mask] = -np.exp(-self.dividend_yield * data.loc[put_mask, 'time_to_expiry']) * \
                         norm.cdf(-data.loc[put_mask, 'd1'])
        
        return delta
    
    def _calculate_gamma(self, data: pd.DataFrame) -> pd.Series:
        """Calculate gamma."""
        return (np.exp(-self.dividend_yield * data['time_to_expiry']) * 
                norm.pdf(data['d1'])) / \
               (data['spx_price'] * data['implied_volatility'] * np.sqrt(data['time_to_expiry']))
    
    def _calculate_theta(self, data: pd.DataFrame) -> pd.Series:
        """Calculate theta."""
        call_mask = data['contract_type'] == 'call'
        put_mask = data['contract_type'] == 'put'
        
        theta = pd.Series(index=data.index, dtype=float)
        
        # Common terms
        term1 = -(data['spx_price'] * norm.pdf(data['d1']) * data['implied_volatility'] * 
                 np.exp(-self.dividend_yield * data['time_to_expiry'])) / \
                (2 * np.sqrt(data['time_to_expiry']))
        
        term2 = self.dividend_yield * data['spx_price'] * np.exp(-self.dividend_yield * data['time_to_expiry'])
        
        term3 = self.risk_free_rate * data['strike_price'] * np.exp(-self.risk_free_rate * data['time_to_expiry'])
        
        # Call theta
        theta[call_mask] = (term1[call_mask] - 
                           term2[call_mask] * norm.cdf(data.loc[call_mask, 'd1']) - 
                           term3[call_mask] * norm.cdf(data.loc[call_mask, 'd2'])) / 365
        
        # Put theta
        theta[put_mask] = (term1[put_mask] + 
                          term2[put_mask] * norm.cdf(-data.loc[put_mask, 'd1']) + 
                          term3[put_mask] * norm.cdf(-data.loc[put_mask, 'd2'])) / 365
        
        return theta
    
    def _calculate_vega(self, data: pd.DataFrame) -> pd.Series:
        """Calculate vega."""
        return (data['spx_price'] * np.exp(-self.dividend_yield * data['time_to_expiry']) * 
                norm.pdf(data['d1']) * np.sqrt(data['time_to_expiry'])) / 100
    
    def _calculate_rho(self, data: pd.DataFrame) -> pd.Series:
        """Calculate rho."""
        call_mask = data['contract_type'] == 'call'
        put_mask = data['contract_type'] == 'put'
        
        rho = pd.Series(index=data.index, dtype=float)
        
        # Call rho
        rho[call_mask] = (data.loc[call_mask, 'strike_price'] * 
                         data.loc[call_mask, 'time_to_expiry'] * 
                         np.exp(-self.risk_free_rate * data.loc[call_mask, 'time_to_expiry']) * 
                         norm.cdf(data.loc[call_mask, 'd2'])) / 100
        
        # Put rho
        rho[put_mask] = (-data.loc[put_mask, 'strike_price'] * 
                        data.loc[put_mask, 'time_to_expiry'] * 
                        np.exp(-self.risk_free_rate * data.loc[put_mask, 'time_to_expiry']) * 
                        norm.cdf(-data.loc[put_mask, 'd2'])) / 100
        
        return rho
    
    def _calculate_vanna(self, data: pd.DataFrame) -> pd.Series:
        """Calculate vanna (dDelta/dVol)."""
        return (-np.exp(-self.dividend_yield * data['time_to_expiry']) * 
                norm.pdf(data['d1']) * data['d2']) / data['implied_volatility']
    
    def _calculate_charm(self, data: pd.DataFrame) -> pd.Series:
        """Calculate charm (dDelta/dTime)."""
        call_mask = data['contract_type'] == 'call'
        put_mask = data['contract_type'] == 'put'
        
        charm = pd.Series(index=data.index, dtype=float)
        
        # Common term
        common_term = (np.exp(-self.dividend_yield * data['time_to_expiry']) * 
                      norm.pdf(data['d1']) * 
                      (2 * (self.risk_free_rate - self.dividend_yield) * data['time_to_expiry'] - 
                       data['d2'] * data['implied_volatility'] * np.sqrt(data['time_to_expiry']))) / \
                     (2 * data['time_to_expiry'] * data['implied_volatility'] * np.sqrt(data['time_to_expiry']))
        
        # Call charm
        charm[call_mask] = -common_term[call_mask] - \
                          self.dividend_yield * np.exp(-self.dividend_yield * data.loc[call_mask, 'time_to_expiry']) * \
                          norm.cdf(data.loc[call_mask, 'd1'])
        
        # Put charm  
        charm[put_mask] = -common_term[put_mask] + \
                         self.dividend_yield * np.exp(-self.dividend_yield * data.loc[put_mask, 'time_to_expiry']) * \
                         norm.cdf(-data.loc[put_mask, 'd1'])
        
        return charm / 365  # Convert to daily
    
    def _calculate_vomma(self, data: pd.DataFrame) -> pd.Series:
        """Calculate vomma (dVega/dVol)."""
        return (data['spx_price'] * np.exp(-self.dividend_yield * data['time_to_expiry']) * 
                norm.pdf(data['d1']) * np.sqrt(data['time_to_expiry']) * 
                data['d1'] * data['d2']) / (data['implied_volatility'] * 100)
    
    def _calculate_speed(self, data: pd.DataFrame) -> pd.Series:
        """Calculate speed (dGamma/dSpot)."""
        return (-data['gamma'] / data['spx_price']) * (data['d1'] / 
                (data['implied_volatility'] * np.sqrt(data['time_to_expiry'])) + 1)
    
    def _analyze_single_greek_trend(self, data: pd.DataFrame, 
                                  greek: str, lookback_days: int) -> Dict[str, Any]:
        """Analyze trend for a single Greek."""
        try:
            # Get recent data
            recent_dates = sorted(data['date'].unique())[-lookback_days:]
            recent_data = data[data['date'].isin(recent_dates)]
            
            # Calculate daily averages
            daily_averages = recent_data.groupby('date')[greek].mean()
            
            # Calculate trend metrics
            values = daily_averages.values
            trend_slope = np.polyfit(range(len(values)), values, 1)[0] if len(values) > 1 else 0
            
            return {
                'current_avg': daily_averages.iloc[-1] if len(daily_averages) > 0 else 0,
                'trend_slope': trend_slope,
                'volatility': np.std(values) if len(values) > 1 else 0,
                'min_value': np.min(values) if len(values) > 0 else 0,
                'max_value': np.max(values) if len(values) > 0 else 0,
                'trend_direction': 'up' if trend_slope > 0 else 'down' if trend_slope < 0 else 'flat'
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing trend for {greek}: {e}")
            return {}
    
    def _get_calculation_summary(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Get summary of Greeks calculations."""
        greek_columns = ['delta', 'gamma', 'theta', 'vega', 'rho']
        available_greeks = [col for col in greek_columns if col in data.columns]
        
        summary = {
            'total_options': len(data),
            'greeks_calculated': available_greeks,
            'calculation_timestamp': pd.Timestamp.now().isoformat()
        }
        
        # Add basic statistics for each Greek
        for greek in available_greeks:
            if greek in data.columns:
                summary[f'{greek}_stats'] = {
                    'mean': float(data[greek].mean()),
                    'std': float(data[greek].std()),
                    'min': float(data[greek].min()),
                    'max': float(data[greek].max())
                }
        
        return summary
    
    def calculate_black_scholes_price(self, spot: float, strike: float, time_to_expiry: float, 
                                    volatility: float, option_type: str = 'call') -> float:
        """
        Calculate Black-Scholes option price.
        
        Args:
            spot: Current underlying price
            strike: Strike price
            time_to_expiry: Time to expiration in years
            volatility: Implied volatility (annualized)
            option_type: 'call' or 'put'
            
        Returns:
            Theoretical option price
        """
        if time_to_expiry <= 0:
            # Option has expired
            if option_type == 'call':
                return max(spot - strike, 0)
            else:
                return max(strike - spot, 0)
        
        if volatility <= 0:
            return 0.0
        
        # Calculate d1 and d2
        d1 = (np.log(spot / strike) + 
              (self.risk_free_rate - self.dividend_yield + 0.5 * volatility**2) * time_to_expiry) / \
             (volatility * np.sqrt(time_to_expiry))
        
        d2 = d1 - volatility * np.sqrt(time_to_expiry)
        
        if option_type == 'call':
            price = (spot * np.exp(-self.dividend_yield * time_to_expiry) * norm.cdf(d1) - 
                    strike * np.exp(-self.risk_free_rate * time_to_expiry) * norm.cdf(d2))
        else:  # put
            price = (strike * np.exp(-self.risk_free_rate * time_to_expiry) * norm.cdf(-d2) - 
                    spot * np.exp(-self.dividend_yield * time_to_expiry) * norm.cdf(-d1))
        
        return max(price, 0.0)
    
    def calculate_vega_bs(self, spot: float, strike: float, time_to_expiry: float, 
                         volatility: float) -> float:
        """
        Calculate vega for implied volatility calculation.
        
        Args:
            spot: Current underlying price
            strike: Strike price  
            time_to_expiry: Time to expiration in years
            volatility: Implied volatility (annualized)
            
        Returns:
            Vega value
        """
        if time_to_expiry <= 0 or volatility <= 0:
            return 0.0
        
        d1 = (np.log(spot / strike) + 
              (self.risk_free_rate - self.dividend_yield + 0.5 * volatility**2) * time_to_expiry) / \
             (volatility * np.sqrt(time_to_expiry))
        
        vega = spot * np.exp(-self.dividend_yield * time_to_expiry) * norm.pdf(d1) * np.sqrt(time_to_expiry)
        return vega / 100  # Convert to 1% change
    
    def calculate_implied_volatility(self, market_price: float, spot: float, strike: float, 
                                   time_to_expiry: float, option_type: str = 'call',
                                   max_iterations: int = 100, tolerance: float = 1e-6) -> float:
        """
        Calculate implied volatility using Brent's method.
        
        Args:
            market_price: Observed market price
            spot: Current underlying price
            strike: Strike price
            time_to_expiry: Time to expiration in years
            option_type: 'call' or 'put'
            max_iterations: Maximum iterations for convergence
            tolerance: Convergence tolerance
            
        Returns:
            Implied volatility (annualized)
        """
        if time_to_expiry <= 0:
            return 0.0
        
        if market_price <= 0:
            return 0.0
        
        # Intrinsic value check
        if option_type == 'call':
            intrinsic = max(spot - strike, 0)
        else:
            intrinsic = max(strike - spot, 0)
        
        if market_price <= intrinsic:
            return 0.0
        
        def price_diff(vol):
            """Function to find root of: market_price - theoretical_price = 0"""
            try:
                theoretical_price = self.calculate_black_scholes_price(
                    spot, strike, time_to_expiry, vol, option_type
                )
                return market_price - theoretical_price
            except:
                return float('inf')
        
        try:
            # Use Brent's method to find implied volatility
            # Search between 0.001 (0.1%) and 5.0 (500%)
            min_vol = 0.001
            max_vol = 5.0
            
            # Check if root exists in the interval
            if price_diff(min_vol) * price_diff(max_vol) > 0:
                # Try a wider range
                max_vol = 10.0
                if price_diff(min_vol) * price_diff(max_vol) > 0:
                    # Return a reasonable default if no root found
                    return 0.20  # 20% default
            
            implied_vol = brentq(price_diff, min_vol, max_vol, 
                                xtol=tolerance, maxiter=max_iterations)
            
            # Sanity check: ensure reasonable volatility range
            if 0.001 <= implied_vol <= 5.0:
                return implied_vol
            else:
                return 0.20  # Default fallback
                
        except Exception as e:
            self.logger.debug(f"IV calculation failed for price={market_price}, "
                            f"spot={spot}, strike={strike}, tte={time_to_expiry}: {e}")
            return 0.20  # Default fallback
    
    def calculate_implied_volatilities(self, options_data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate implied volatilities for a DataFrame of options.
        
        Args:
            options_data: DataFrame with columns: market_price, spx_price, strike_price, 
                         dte, contract_type
                         
        Returns:
            DataFrame with calculated implied_volatility column
        """
        data = options_data.copy()
        
        # Required columns
        required_cols = ['spx_price', 'strike_price', 'dte', 'contract_type']
        missing_cols = [col for col in required_cols if col not in data.columns]
        
        if missing_cols:
            raise ValueError(f"Missing required columns: {missing_cols}")
        
        # Determine market price column (try multiple possibilities)
        price_cols = ['close', 'market_price', 'price', 'mid_price']
        market_price_col = None
        
        for col in price_cols:
            if col in data.columns:
                market_price_col = col
                break
        
        if market_price_col is None:
            # Calculate mid price from bid/ask if available
            if 'bid' in data.columns and 'ask' in data.columns:
                data['mid_price'] = (data['bid'] + data['ask']) / 2
                market_price_col = 'mid_price'
            else:
                raise ValueError("No market price column found. Need one of: close, market_price, price, mid_price, or bid/ask")
        
        # Convert DTE to time to expiration in years
        data['time_to_expiry'] = data['dte'] / 365.0
        
        # Calculate implied volatility for each option
        implied_vols = []
        
        for idx, row in data.iterrows():
            market_price = row[market_price_col]
            spot = row['spx_price']
            strike = row['strike_price']
            time_to_expiry = row['time_to_expiry']
            option_type = 'call' if row['contract_type'] == 'call' else 'put'
            
            iv = self.calculate_implied_volatility(
                market_price, spot, strike, time_to_expiry, option_type
            )
            implied_vols.append(iv)
        
        data['implied_volatility'] = implied_vols
        
        self.logger.info(f"Calculated implied volatilities for {len(data):,} options")
        self.logger.info(f"IV range: {min(implied_vols):.1%} - {max(implied_vols):.1%}")
        
        return data
    
    def __str__(self) -> str:
        return "GreeksCalculator"
    
    def __repr__(self) -> str:
        return f"GreeksCalculator(risk_free_rate={self.risk_free_rate}, dividend_yield={self.dividend_yield})"
