"""
Base Analyzer class for the SPX Options Trading System.

This module provides the abstract base class for all analysis components,
ensuring consistent interface for data analysis and metric calculation.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from datetime import datetime
import pandas as pd
import logging

from src.base.interfaces import IConfigurable

logger = logging.getLogger(__name__)


class BaseAnalyzer(IConfigurable, ABC):
    """
    Abstract base class for all analysis components.
    
    Analyzers are responsible for calculating metrics, performing
    analysis, and generating insights from market data.
    """
    
    def __init__(self, name: str):
        self.name = name
        self.config = {}
        self.is_configured = False
        self.analysis_cache = {}
        self.cache_enabled = True
        
        # Initialize logger
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def configure(self, config: Dict[str, Any]) -> None:
        """Configure the analyzer with provided settings."""
        self.config = config
        self.cache_enabled = config.get('enable_cache', True)
        self._validate_required_config()
        self.is_configured = True
        self.logger.info(f"Analyzer {self.name} configured successfully")
    
    def validate_config(self) -> bool:
        """Validate the current configuration."""
        try:
            self._validate_required_config()
            return True
        except Exception as e:
            self.logger.error(f"Configuration validation failed: {e}")
            return False
    
    @abstractmethod
    def _validate_required_config(self) -> None:
        """Validate analyzer-specific required configuration."""
        pass
    
    @abstractmethod
    def analyze(self, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """
        Perform analysis on the provided data.
        
        Args:
            data: Input data for analysis
            **kwargs: Additional analyzer-specific parameters
            
        Returns:
            Dict containing analysis results and metrics
        """
        pass
    
    def analyze_with_cache(self, data: pd.DataFrame, 
                          cache_key: Optional[str] = None, 
                          **kwargs) -> Dict[str, Any]:
        """
        Perform analysis with optional caching.
        
        Args:
            data: Input data for analysis
            cache_key: Optional cache key for results
            **kwargs: Additional analyzer-specific parameters
            
        Returns:
            Dict containing analysis results and metrics
        """
        if not self.is_configured:
            raise RuntimeError(f"Analyzer {self.name} is not configured")
        
        # Generate cache key if not provided
        if cache_key is None and self.cache_enabled:
            cache_key = self._generate_cache_key(data, **kwargs)
        
        # Check cache
        if self.cache_enabled and cache_key and cache_key in self.analysis_cache:
            self.logger.debug(f"Cache hit for {self.name}: {cache_key}")
            return self.analysis_cache[cache_key]
        
        try:
            # Perform analysis
            results = self.analyze(data, **kwargs)
            
            # Cache results
            if self.cache_enabled and cache_key:
                self.analysis_cache[cache_key] = results
                self.logger.debug(f"Cached results for {self.name}: {cache_key}")
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error in analyzer {self.name}: {e}")
            raise
    
    def _generate_cache_key(self, data: pd.DataFrame, **kwargs) -> str:
        """Generate a cache key based on data and parameters."""
        # Simple cache key based on data shape and hash of kwargs
        data_key = f"{len(data)}_{hash(str(data.columns.tolist()))}"
        kwargs_key = hash(str(sorted(kwargs.items())))
        return f"{self.name}_{data_key}_{kwargs_key}"
    
    def clear_cache(self) -> None:
        """Clear the analysis cache."""
        cache_size = len(self.analysis_cache)
        self.analysis_cache.clear()
        self.logger.info(f"Cleared {cache_size} cached results for {self.name}")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """Get cache information."""
        return {
            'cache_enabled': self.cache_enabled,
            'cache_size': len(self.analysis_cache),
            'cache_keys': list(self.analysis_cache.keys())
        }
    
    def enable_cache(self) -> None:
        """Enable caching."""
        self.cache_enabled = True
        self.logger.info(f"Cache enabled for {self.name}")
    
    def disable_cache(self) -> None:
        """Disable caching and clear existing cache."""
        self.cache_enabled = False
        self.clear_cache()
        self.logger.info(f"Cache disabled for {self.name}")
    
    def get_analyzer_info(self) -> Dict[str, Any]:
        """Get analyzer information."""
        return {
            'name': self.name,
            'is_configured': self.is_configured,
            'cache_info': self.get_cache_info()
        }
    
    def __str__(self) -> str:
        return f"{self.name}"
    
    def __repr__(self) -> str:
        return f"BaseAnalyzer(name='{self.name}', configured={self.is_configured})"
