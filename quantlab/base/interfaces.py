"""
Interfaces for the SPX Options Trading System.

This module defines the contracts that all components must implement
to ensure proper integration and extensibility.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import pandas as pd


class IConfigurable(ABC):
    """Interface for configurable components."""
    
    @abstractmethod
    def configure(self, config: Dict[str, Any]) -> None:
        """Configure the component with provided settings."""
        pass
    
    @abstractmethod
    def validate_config(self) -> bool:
        """Validate the current configuration."""
        pass


class IDataRetrieval(ABC):
    """Interface for data retrieval components."""
    
    @abstractmethod
    def load_options_data(self, start_date: Optional[datetime] = None, 
                         end_date: Optional[datetime] = None) -> pd.DataFrame:
        """Load options data for the specified date range."""
        pass
    
    @abstractmethod
    def get_trading_dates(self, start_date: datetime, end_date: datetime) -> List[datetime]:
        """Get list of available trading dates."""
        pass
    
    @abstractmethod
    def validate_data(self, data: pd.DataFrame) -> bool:
        """Validate data quality and completeness."""
        pass


class IGreeksCalculator(ABC):
    """Interface for Greeks calculation components."""
    
    @abstractmethod
    def calculate_greeks(self, options_data: pd.DataFrame) -> pd.DataFrame:
        """Calculate all Greeks for the provided options data."""
        pass
    
    @abstractmethod
    def calculate_second_order_greeks(self, options_data: pd.DataFrame) -> pd.DataFrame:
        """Calculate second-order Greeks (gamma, vanna, charm, etc.)."""
        pass
    
    @abstractmethod
    def analyze_greek_trends(self, options_data: pd.DataFrame, 
                           lookback_days: int = 5) -> Dict[str, Any]:
        """Analyze trends in Greeks over time."""
        pass


class IStrikeSelector(ABC):
    """Interface for strike selection components."""
    
    @abstractmethod
    def select_calendar_spread_strikes(self, options_data: pd.DataFrame, 
                                     spx_price: float, 
                                     strategy_type: str = 'call') -> Optional[Dict[str, Any]]:
        """Select optimal strikes for calendar spread."""
        pass
    
    @abstractmethod
    def validate_liquidity(self, option_data: pd.Series) -> bool:
        """Validate option liquidity requirements."""
        pass
    
    @abstractmethod
    def calculate_spread_metrics(self, short_option: pd.Series, 
                               long_option: pd.Series) -> Dict[str, float]:
        """Calculate spread-specific metrics."""
        pass


class ITechnicalAnalyzer(ABC):
    """Interface for technical analysis components."""
    
    @abstractmethod
    def calculate_vix_indicators(self, date: datetime) -> Dict[str, float]:
        """Calculate VIX-based indicators."""
        pass
    
    @abstractmethod
    def analyze_volatility_environment(self, options_data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze current volatility environment."""
        pass
    
    @abstractmethod
    def calculate_market_indicators(self, spx_price: float, 
                                  previous_close: float) -> Dict[str, float]:
        """Calculate market-based indicators."""
        pass


class IBacktester(ABC):
    """Interface for backtesting components."""
    
    @abstractmethod
    def run_backtest(self, options_data: pd.DataFrame, 
                    strategy: 'BaseStrategy') -> Dict[str, Any]:
        """Run comprehensive backtest."""
        pass
    
    @abstractmethod
    def execute_trade(self, trade_signal: Dict[str, Any], 
                     options_data: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """Execute a single trade."""
        pass
    
    @abstractmethod
    def calculate_pnl(self, entry_data: Dict[str, Any], 
                     exit_data: Dict[str, Any], 
                     position_size: int) -> Dict[str, float]:
        """Calculate trade P&L."""
        pass
