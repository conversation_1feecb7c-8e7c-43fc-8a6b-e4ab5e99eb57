"""
Base Data Provider class for the SPX Options Trading System.

This module provides the abstract base class for all data providers,
ensuring consistent interface for data loading and validation.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from datetime import datetime
import pandas as pd
import logging

from src.base.interfaces import IConfigurable, IDataRetrieval

logger = logging.getLogger(__name__)


class BaseDataProvider(IConfigurable, IDataRetrieval, ABC):
    """
    Abstract base class for all data providers.
    
    Data providers are responsible for loading, validating, and
    preprocessing market data from various sources.
    """
    
    def __init__(self, name: str, source_type: str):
        self.name = name
        self.source_type = source_type
        self.config = {}
        self.is_configured = False
        self.data_cache = {}
        self.cache_enabled = True
        
        # Initialize logger
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def configure(self, config: Dict[str, Any]) -> None:
        """Configure the data provider with provided settings."""
        self.config = config
        self.cache_enabled = config.get('enable_cache', True)
        self._validate_required_config()
        self.is_configured = True
        self.logger.info(f"Data provider {self.name} configured successfully")
    
    def validate_config(self) -> bool:
        """Validate the current configuration."""
        try:
            self._validate_required_config()
            return True
        except Exception as e:
            self.logger.error(f"Configuration validation failed: {e}")
            return False
    
    @abstractmethod
    def _validate_required_config(self) -> None:
        """Validate data provider-specific required configuration."""
        pass
    
    @abstractmethod
    def load_options_data(self, start_date: Optional[datetime] = None, 
                         end_date: Optional[datetime] = None) -> pd.DataFrame:
        """Load options data for the specified date range."""
        pass
    
    @abstractmethod
    def get_trading_dates(self, start_date: datetime, end_date: datetime) -> List[datetime]:
        """Get list of available trading dates."""
        pass
    
    @abstractmethod
    def validate_data(self, data: pd.DataFrame) -> bool:
        """Validate data quality and completeness."""
        pass
    
    def load_options_data_with_cache(self, start_date: Optional[datetime] = None, 
                                   end_date: Optional[datetime] = None) -> pd.DataFrame:
        """Load options data with optional caching."""
        if not self.is_configured:
            raise RuntimeError(f"Data provider {self.name} is not configured")
        
        # Generate cache key
        cache_key = None
        if self.cache_enabled:
            cache_key = f"{start_date}_{end_date}"
            if cache_key in self.data_cache:
                self.logger.debug(f"Cache hit for data: {cache_key}")
                return self.data_cache[cache_key]
        
        try:
            # Load data
            data = self.load_options_data(start_date, end_date)
            
            # Validate data
            if not self.validate_data(data):
                raise ValueError("Data validation failed")
            
            # Cache data
            if self.cache_enabled and cache_key:
                self.data_cache[cache_key] = data
                self.logger.debug(f"Cached data: {cache_key}")
            
            return data
            
        except Exception as e:
            self.logger.error(f"Error loading data: {e}")
            raise
    
    def preprocess_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Preprocess the loaded data.
        
        This method can be overridden by subclasses to implement
        specific preprocessing logic.
        """
        return data
    
    def get_data_info(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Get information about the loaded data."""
        if data.empty:
            return {'error': 'No data available'}
        
        return {
            'total_records': len(data),
            'date_range': {
                'start': data['date'].min() if 'date' in data.columns else None,
                'end': data['date'].max() if 'date' in data.columns else None
            },
            'unique_dates': data['date'].nunique() if 'date' in data.columns else None,
            'columns': data.columns.tolist(),
            'memory_usage_mb': data.memory_usage(deep=True).sum() / 1024 / 1024
        }
    
    def clear_cache(self) -> None:
        """Clear the data cache."""
        cache_size = len(self.data_cache)
        self.data_cache.clear()
        self.logger.info(f"Cleared {cache_size} cached datasets for {self.name}")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """Get cache information."""
        return {
            'cache_enabled': self.cache_enabled,
            'cache_size': len(self.data_cache),
            'cache_keys': list(self.data_cache.keys())
        }
    
    def enable_cache(self) -> None:
        """Enable caching."""
        self.cache_enabled = True
        self.logger.info(f"Cache enabled for {self.name}")
    
    def disable_cache(self) -> None:
        """Disable caching and clear existing cache."""
        self.cache_enabled = False
        self.clear_cache()
        self.logger.info(f"Cache disabled for {self.name}")
    
    def __str__(self) -> str:
        return f"{self.name} ({self.source_type})"
    
    def __repr__(self) -> str:
        return (f"BaseDataProvider(name='{self.name}', source_type='{self.source_type}', "
                f"configured={self.is_configured})")
