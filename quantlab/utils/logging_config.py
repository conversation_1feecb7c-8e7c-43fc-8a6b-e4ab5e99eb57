#!/usr/bin/env python3
"""
Logging Configuration for Optionstrat Program

Provides centralized logging configuration for the entire application,
including the data downloader integration.
"""

import os
import logging
import logging.handlers
from pathlib import Path
from datetime import datetime
from typing import Optional


def setup_logging(log_level: str = "INFO", 
                 log_dir: str = "logs",
                 enable_file_logging: bool = True,
                 enable_console_logging: bool = True,
                 max_log_size_mb: int = 10,
                 backup_count: int = 5) -> logging.Logger:
    """
    Set up comprehensive logging for the optionstrat program.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_dir: Directory for log files
        enable_file_logging: Whether to log to files
        enable_console_logging: Whether to log to console
        max_log_size_mb: Maximum size of each log file in MB
        backup_count: Number of backup log files to keep
        
    Returns:
        Configured root logger
    """
    # Create logs directory
    log_path = Path(log_dir)
    log_path.mkdir(exist_ok=True)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear any existing handlers
    root_logger.handlers.clear()
    
    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
    )
    simple_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # Console handler
    if enable_console_logging:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(simple_formatter)
        root_logger.addHandler(console_handler)
    
    # File handlers
    if enable_file_logging:
        # Main application log
        main_log_file = log_path / f"optionstrat_{datetime.now().strftime('%Y%m%d')}.log"
        main_handler = logging.handlers.RotatingFileHandler(
            main_log_file,
            maxBytes=max_log_size_mb * 1024 * 1024,
            backupCount=backup_count
        )
        main_handler.setLevel(logging.DEBUG)
        main_handler.setFormatter(detailed_formatter)
        root_logger.addHandler(main_handler)
        
        # Error log (errors and above only)
        error_log_file = log_path / f"optionstrat_errors_{datetime.now().strftime('%Y%m%d')}.log"
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=max_log_size_mb * 1024 * 1024,
            backupCount=backup_count
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(detailed_formatter)
        root_logger.addHandler(error_handler)
        
        # Data downloader specific log
        data_log_file = log_path / f"data_downloader_{datetime.now().strftime('%Y%m%d')}.log"
        data_handler = logging.handlers.RotatingFileHandler(
            data_log_file,
            maxBytes=max_log_size_mb * 1024 * 1024,
            backupCount=backup_count
        )
        data_handler.setLevel(logging.DEBUG)
        data_handler.setFormatter(detailed_formatter)
        
        # Add filter for data-related loggers
        data_handler.addFilter(DataLoggerFilter())
        root_logger.addHandler(data_handler)
    
    # Configure specific logger levels
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('matplotlib').setLevel(logging.WARNING)
    
    return root_logger


class DataLoggerFilter(logging.Filter):
    """Filter to capture data-related log messages."""
    
    def filter(self, record):
        """Filter log records for data-related messages."""
        data_related_names = [
            'quantlab.data',
            'quantlab.greeks',
            'quantlab.config',
            'main'  # Include main module data operations
        ]
        
        return any(record.name.startswith(name) for name in data_related_names)


class ContextualLogger:
    """
    Contextual logger that adds operation context to log messages.
    Useful for tracking data download operations, analysis steps, etc.
    """
    
    def __init__(self, logger_name: str, context: Optional[str] = None):
        self.logger = logging.getLogger(logger_name)
        self.context = context
    
    def _format_message(self, message: str) -> str:
        """Add context to log message if available."""
        if self.context:
            return f"[{self.context}] {message}"
        return message
    
    def debug(self, message: str, *args, **kwargs):
        self.logger.debug(self._format_message(message), *args, **kwargs)
    
    def info(self, message: str, *args, **kwargs):
        self.logger.info(self._format_message(message), *args, **kwargs)
    
    def warning(self, message: str, *args, **kwargs):
        self.logger.warning(self._format_message(message), *args, **kwargs)
    
    def error(self, message: str, *args, **kwargs):
        self.logger.error(self._format_message(message), *args, **kwargs)
    
    def critical(self, message: str, *args, **kwargs):
        self.logger.critical(self._format_message(message), *args, **kwargs)
    
    def set_context(self, context: str):
        """Update the logging context."""
        self.context = context
    
    def clear_context(self):
        """Clear the logging context."""
        self.context = None


def get_contextual_logger(logger_name: str, context: Optional[str] = None) -> ContextualLogger:
    """
    Get a contextual logger instance.
    
    Args:
        logger_name: Name of the logger
        context: Optional context string
        
    Returns:
        ContextualLogger instance
    """
    return ContextualLogger(logger_name, context)


def log_function_call(func):
    """
    Decorator to log function calls with timing information.
    Useful for tracking data download and processing operations.
    """
    import functools
    import time
    
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        logger = logging.getLogger(func.__module__)
        start_time = time.time()
        
        # Log function entry
        logger.debug(f"Entering {func.__name__} with args={len(args)}, kwargs={list(kwargs.keys())}")
        
        try:
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            logger.debug(f"Completed {func.__name__} in {duration:.2f}s")
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"Error in {func.__name__} after {duration:.2f}s: {e}")
            raise
    
    return wrapper


def setup_optionstrat_logging(log_level: str = None) -> logging.Logger:
    """
    Set up logging specifically for the optionstrat program.
    Reads configuration from environment variables if available.
    
    Args:
        log_level: Override log level
        
    Returns:
        Configured root logger
    """
    # Get configuration from environment
    log_level = log_level or os.getenv('LOG_LEVEL', 'INFO')
    log_dir = os.getenv('LOGS_DIR', 'logs')
    enable_file_logging = os.getenv('ENABLE_FILE_LOGGING', 'true').lower() == 'true'
    enable_console_logging = os.getenv('ENABLE_CONSOLE_LOGGING', 'true').lower() == 'true'
    
    return setup_logging(
        log_level=log_level,
        log_dir=log_dir,
        enable_file_logging=enable_file_logging,
        enable_console_logging=enable_console_logging
    )


# Initialize logging when module is imported
if not logging.getLogger().handlers:
    setup_optionstrat_logging()
