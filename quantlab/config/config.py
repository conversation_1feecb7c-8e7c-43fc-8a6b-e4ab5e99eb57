#!/usr/bin/env python3
"""
Configuration Management for SPX Iron Butterfly Trading System
Centralized configuration with trading parameters, data settings, and system options.
"""

import os
from datetime import time
from typing import Dict, Any, Optional
from dataclasses import dataclass

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()  # Load .env file if it exists
except ImportError:
    # dotenv not installed, skip loading .env file
    pass

@dataclass
class IronButterflyConfig:
    """Iron Butterfly strategy configuration parameters."""
    
    # Position Sizing
    POSITION_SIZE: int = 10                    # Contracts per trade
    
    # Entry/Exit Timing
    ENABLE_MULTIPLE_ENTRIES: bool = True       # Multiple daily entry windows
    ENTRY_WINDOWS: list = None                 # Will be set in __post_init__
    EXIT_TIME: time = time(15, 45, 0)          # 3:45 PM ET on expiration day
    ALLOW_INTRADAY_ENTRIES: bool = True        # Allow entries throughout trading day
    
    # Strategy Parameters
    WING_WIDTH: int = 75                       # Points wide on each side
    PROFIT_TARGET: float = 0.15                # 15% profit target
    MIN_HOLD_DAYS: int = 0                     # Allow same-day exits
    STRIKE_INCREMENT: int = 25                 # SPX strikes in 25-point increments
    COMMISSION_PER_CONTRACT: float = 1.25      # Commission per contract
    
    # Ultra-Aggressive Mode Filters (Relaxed)
    MAX_DELTA_THRESHOLD: float = 0.50          # Very relaxed delta tolerance
    MIN_IV_THRESHOLD: float = 0.01             # 1% minimum IV
    MIN_GAMMA_THRESHOLD: float = 0.001         # Ultra-low gamma requirement
    BID_ASK_SPREAD_FACTOR: float = 0.2         # Better execution assumption
    
    # Filter Control Flags (Disabled for maximum trades)
    ENABLE_DELTA_FILTER: bool = False
    ENABLE_IV_FILTER: bool = False
    ENABLE_GREEKS_FILTER: bool = False
    ENABLE_SECOND_ORDER_GREEKS_FILTER: bool = False
    ENABLE_MOMENTUM_FILTER: bool = False
    
    # Extended DTE Range (Expanded for more opportunities)
    DTE_RANGE_PRIMARY: list = None             # 0-21 DTE, set in __post_init__
    DTE_RANGE_EXTENDED: list = None            # 22-35 DTE, set in __post_init__
    
    def __post_init__(self):
        """Initialize complex fields after object creation."""
        if self.ENTRY_WINDOWS is None:
            self.ENTRY_WINDOWS = [
                (time(9, 30, 0), time(10, 0, 0)),    # Market open window
                (time(10, 30, 0), time(11, 0, 0)),   # Mid-morning window
                (time(11, 30, 0), time(12, 0, 0)),   # Pre-lunch window
                (time(12, 30, 0), time(13, 0, 0)),   # Post-lunch window
                (time(13, 30, 0), time(14, 0, 0)),   # Early afternoon window
                (time(14, 30, 0), time(15, 0, 0)),   # Main window
                (time(15, 0, 0), time(15, 30, 0))    # Late day window
            ]
        
        if self.DTE_RANGE_PRIMARY is None:
            self.DTE_RANGE_PRIMARY = list(range(0, 22))  # 0-21 DTE
        
        if self.DTE_RANGE_EXTENDED is None:
            self.DTE_RANGE_EXTENDED = list(range(22, 36))  # 22-35 DTE

@dataclass
class DataConfig:
    """Data download and processing configuration."""
    
    # File Paths
    DATA_DIR: str = "data"
    REPORTS_DIR: str = "reports"
    LOGS_DIR: str = "logs"
    RESULTS_DIR: str = "results"
    SCRIPTS_DIR: str = "scripts"
    
    # Data Download
    DOWNLOAD_MONTHS: int = 16                  # Default months of data to download
    
    # API Configuration
    POLYGON_BASE_URL: str = "https://api.polygon.io"
    RATE_LIMIT_DELAY: float = 0.1              # Seconds between API calls
    MAX_RETRIES: int = 3                       # API retry attempts
    TIMEOUT: int = 30                          # Request timeout seconds
    
    # Data Processing
    CHUNK_SIZE: int = 10000                    # Processing chunk size
    PARALLEL_WORKERS: int = 10                 # Number of parallel workers
    
    # File Naming
    MASTER_FILE: str = "spx_options_master_with_greeks.parquet"
    METADATA_FILE: str = "spx_download_metadata.parquet"

@dataclass
class ReportConfig:
    """Report generation configuration."""
    
    # PDF Settings
    PDF_TITLE: str = "SPX Iron Butterfly Strategy Analysis"
    PDF_AUTHOR: str = "SPX Options Trading System"
    PDF_SUBJECT: str = "Ultra-Aggressive Iron Butterfly Report"
    
    # Chart Settings
    CHART_WIDTH: int = 12
    CHART_HEIGHT: int = 8
    CHART_DPI: int = 300
    
    # Report Sections
    INCLUDE_TRADE_TABLE: bool = True
    INCLUDE_EQUITY_CURVE: bool = True
    INCLUDE_PERFORMANCE_METRICS: bool = True
    INCLUDE_LAST_TRADES: bool = True
    MAX_TRADES_IN_TABLE: int = 5               # Show last 5 trades

class Config:
    """Main configuration class with all settings."""
    
    def __init__(self):
        self.iron_butterfly = IronButterflyConfig()
        self.data = DataConfig()
        self.report = ReportConfig()
        
        # Load environment variables
        self._load_env_overrides()
    
    def _load_env_overrides(self):
        """Load configuration overrides from environment variables."""
        
        # API Key (required)
        api_key = os.getenv('POLYGON_API_KEY')
        if not api_key:
            raise ValueError("POLYGON_API_KEY environment variable is required")
        self.api_key = api_key
        
        # Optional overrides
        if os.getenv('IB_POSITION_SIZE'):
            self.iron_butterfly.POSITION_SIZE = int(os.getenv('IB_POSITION_SIZE'))
        
        if os.getenv('DATA_DIR'):
            self.data.DATA_DIR = os.getenv('DATA_DIR')
        
        if os.getenv('REPORTS_DIR'):
            self.data.REPORTS_DIR = os.getenv('REPORTS_DIR')
        
        if os.getenv('DOWNLOAD_MONTHS'):
            self.data.DOWNLOAD_MONTHS = int(os.getenv('DOWNLOAD_MONTHS'))
    
    def get_file_paths(self) -> Dict[str, str]:
        """Get all configured file paths."""
        return {
            'data_dir': self.data.DATA_DIR,
            'reports_dir': self.data.REPORTS_DIR,
            'logs_dir': self.data.LOGS_DIR,
            'results_dir': self.data.RESULTS_DIR,
            'scripts_dir': self.data.SCRIPTS_DIR,
            'master_file': os.path.join(self.data.DATA_DIR, self.data.MASTER_FILE),
            'metadata_file': os.path.join(self.data.DATA_DIR, self.data.METADATA_FILE)
        }
    
    def get_api_config(self) -> Dict[str, Any]:
        """Get API configuration."""
        return {
            'api_key': self.api_key,
            'base_url': self.data.POLYGON_BASE_URL,
            'rate_limit_delay': self.data.RATE_LIMIT_DELAY,
            'max_retries': self.data.MAX_RETRIES,
            'timeout': self.data.TIMEOUT
        }
    
    def validate_config(self) -> bool:
        """Validate configuration settings."""
        try:
            # Check required API key
            if not self.api_key:
                raise ValueError("API key is required")
            
            # Validate position size
            if self.iron_butterfly.POSITION_SIZE <= 0:
                raise ValueError("Position size must be positive")
            
            # Validate file paths
            os.makedirs(self.data.DATA_DIR, exist_ok=True)
            os.makedirs(self.data.REPORTS_DIR, exist_ok=True)
            os.makedirs(self.data.LOGS_DIR, exist_ok=True)
            os.makedirs(self.data.RESULTS_DIR, exist_ok=True)
            os.makedirs(self.data.SCRIPTS_DIR, exist_ok=True)
            
            return True
            
        except Exception as e:
            print(f"❌ Configuration validation failed: {e}")
            return False

# Global configuration instance
_config = None

def get_config() -> Config:
    """Get the global configuration instance."""
    global _config
    if _config is None:
        _config = Config()
        if not _config.validate_config():
            raise RuntimeError("Configuration validation failed")
    return _config

def reload_config() -> Config:
    """Reload configuration from environment."""
    global _config
    _config = None
    return get_config()

# Convenience functions for common config access
def get_iron_butterfly_config() -> IronButterflyConfig:
    """Get Iron Butterfly configuration."""
    return get_config().iron_butterfly

def get_data_config() -> DataConfig:
    """Get data configuration."""
    return get_config().data

def get_report_config() -> ReportConfig:
    """Get report configuration."""
    return get_config().report

def get_api_key() -> str:
    """Get API key."""
    return get_config().api_key

def get_file_paths() -> Dict[str, str]:
    """Get file paths."""
    return get_config().get_file_paths()

if __name__ == "__main__":
    # Test configuration
    try:
        config = get_config()
        print("✅ Configuration loaded successfully")
        print(f"📊 Position Size: {config.iron_butterfly.POSITION_SIZE} contracts")
        print(f"📁 Data Directory: {config.data.DATA_DIR}")
        print(f"🔑 API Key: {'*' * 20}...{config.api_key[-4:]}")
        print(f"⚡ Ultra-Aggressive Mode: {'Enabled' if not config.iron_butterfly.ENABLE_DELTA_FILTER else 'Disabled'}")
        print(f"🕐 Entry Windows: {len(config.iron_butterfly.ENTRY_WINDOWS)} daily windows")
    except Exception as e:
        print(f"❌ Configuration error: {e}")