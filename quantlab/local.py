from quantlab.optalpha import OptAlpha

import os
import pytz
import numpy as np
import pandas as pd 
import zipfile

from datetime import datetime
from dateutil.relativedelta import relativedelta
from quantlab.optalpha import HEDGE_TARGET, AbstractImplementationException

class LocalSPXData(OptAlpha):

    def archive_constructor(self,dt):
        if dt.year >= 1990 and dt.year <= 2021:
            return "ALLSPX_1990_2021.zip"
        elif dt.year == 2022:
            return "ALLSPX_2022.zip"
        elif dt.year == 2023:
            return "ALLSPX_2023.zip"

    def filename_constructor(self,dt):
        return f"SPX_{dt.year}.csv"

    def screen_universe(self,df,universe):
        df = df.loc[np.where(np.logical_or(df.volume == 0, df.openinterest == 0),False,True)]
        df.expiration = pd.to_datetime(df.expiration).dt.tz_localize("UTC")
        df.quotedate = pd.to_datetime(df.quotedate).dt.tz_localize("UTC")
        df["dte"] = (df.expiration - df.quotedate).apply(lambda x: x.days)
        df = df.drop(columns=[" exchange","optionext","optionalias","bid","ask","theta","vega","gamma","IVBid","IVAsk"])        
        return df
    
    def instantiate_variables(self):
        self.unzipped = set()
        self.loaded = set()
    
    def load_buffer(self,load_from,test_end,min_buffer_len=100,min_hist_len=2): 
        dir = "/Users/<USER>/Desktop/spxopt/"
        self.data_buffer = self.data_buffer[-min_hist_len:]
        self.data_buffer_idx = self.data_buffer_idx[-min_hist_len:]
        while len(self.data_buffer) < min_buffer_len:
            while self.filename_constructor(dt=load_from) in self.loaded:
                load_from += relativedelta(days=1)
            datfile = self.filename_constructor(dt=load_from)
            if datfile not in self.unzipped:
                an = self.archive_constructor(dt=load_from)
                with zipfile.ZipFile(dir+an,"r") as archive:
                    archive.extractall(path=dir+"pyrun/")
                    all_files=archive.namelist()
                    self.unzipped = self.unzipped.union(set(all_files))
            optdat = pd.read_csv(dir+"pyrun/"+datfile)
            optdat = self.screen_universe(df=optdat,universe=self.instruments)
            for date in sorted(set(optdat.quotedate)):
                self.data_buffer.append(optdat.loc[optdat.quotedate == date])
                self.data_buffer_idx.append(date.to_pydatetime())
            os.remove(dir+"pyrun/"+datfile)
            self.loaded.add(datfile)

    def compute_signals(self,date,capital):
        raise AbstractImplementationException()
    
    def compute_metas(self):
        raise AbstractImplementationException()
    
class LocalSSEData(OptAlpha):
    
    def instantiate_variables(self):
        self.loaded = set()

    def archive_constructor(self,dt):
       return f"bb_{dt.year}_{dt.strftime('%B')}.zip"

    def filename_constructor(self,dt):
       return f"bb_options_{str(dt.date()).replace('-','')}.csv"

    def screen_universe(self,df,universe):
        df = df.loc[np.where(np.logical_or(df.Volume == 0, df.OpenInterest == 0),False,True)]
        df["in_universe"] = df.UnderlyingSymbol.apply(lambda x: x in universe)
        df = df.loc[df.in_universe].drop(columns=["in_universe"])
        df.Expiration = pd.to_datetime(df.Expiration).dt.tz_localize("UTC")
        df.DataDate = pd.to_datetime(df.DataDate).dt.tz_localize("UTC")
        df["dte"] = (df.Expiration - df.DataDate).apply(lambda x: x.days)
        df = df.drop(columns=["Exchange","OptionExt","Bid","Ask"])
        df=df.rename(columns={
            "OptionRoot":"optionroot",
            "UnderlyingSymbol": "underlying",
            "UnderlyingPrice": "underlying_last",
            "Type": "type",
            "Expiration": "expiration",
            "DataDate": "quotedate",
            "Strike": "strike",
            "Last": "last",
            "OpenInterest": "openinterest",
            "Volume": "volume"
        })
        return df
        
    def load_buffer(self,load_from,test_end,min_buffer_len=100,min_hist_len=2): 
        import os,zipfile
        import threading
        assert min_hist_len >= 2 and min_buffer_len >= min_hist_len
        dir = "/Users/<USER>/Desktop/optdat/"
        self.data_buffer = self.data_buffer[-min_hist_len:]
        self.data_buffer_idx = self.data_buffer_idx[-min_hist_len:]

        while len(self.data_buffer) < min_buffer_len:
            while self.archive_constructor(dt=load_from) in self.loaded:
                load_from += relativedelta(days=1)
            if load_from > test_end: break
            an=self.archive_constructor(dt=load_from)
            with zipfile.ZipFile(dir+an,'r') as archive:
                archive.extractall(path=dir+"pyrun/")
                all_files=archive.namelist()
                for file in all_files:
                    if "L2_options" in file: os.remove(dir+"pyrun/"+file)
                all_files = sorted(all_files)
                load_1 = [None]*len(all_files)
                load_2 = [None]*len(all_files)
                def poll(i):
                    datfile = all_files[i]
                    yyyymmdd=datfile.split('.csv')[0].split('_')[-1]
                    dt_load = datetime(
                        year=int(yyyymmdd[:4]),
                        month=int(yyyymmdd[4:6]),
                        day=int(yyyymmdd[6:]),
                        tzinfo=pytz.utc
                    )
                    if dt_load > test_end:
                        os.remove(dir+"pyrun/"+datfile)
                        return 
                    optdat = pd.read_csv(dir+"pyrun/"+datfile)                        
                    os.remove(dir+"pyrun/"+datfile)
                    optdat = self.screen_universe(
                        df=optdat,
                        universe=self.instruments
                    )
                    load_1[i]=optdat
                    load_2[i]=dt_load
                threads=[]  
                for i in range(len(all_files)): threads.append(threading.Thread(target=poll, args=(i,)))
                for thread in threads: thread.start()
                for thread in threads: thread.join()
                self.data_buffer.extend([_ for _ in load_1 if _ is not None]) 
                self.data_buffer_idx.extend([_ for _ in load_2 if _ is not None]) 
                self.loaded.add(an) 
        return
    
    def compute_signals(self,date,capital):
        raise AbstractImplementationException()

    def compute_metas(self):
        raise AbstractImplementationException()

