import pytz
import numpy as np
import pandas as pd 

from datetime import datetime
from quantlab.local import LocalSPXData,LocalSSEData
from quantlab.optalpha import REBALANCER,OPTION_SELECTOR,HEDGE_TARGET,HEDGE_TRIGGER

from quantyhlib.general_utils import load_pickle,save_pickle

import requests
import yfinance
import threading
from bs4 import BeautifulSoup

def get_sp500_tickers():
    res = requests.get("https://en.wikipedia.org/wiki/List_of_S%26P_500_companies")
    soup = BeautifulSoup(res.content,'html')
    table = soup.find_all('table')[0] 
    df = pd.read_html(str(table))
    tickers = list(df[0].Symbol)
    return tickers

def get_history(ticker, period_start, period_end, granularity="1d", tries=0):
    try:
        df = yfinance.Ticker(ticker).history(
            start=period_start,
            end=period_end,
            interval=granularity,
            auto_adjust=True
        ).reset_index()
    except Exception as err:
        if tries < 5:
            return get_history(ticker, period_start, period_end, granularity, tries+1)
        return pd.DataFrame()
    
    df = df.rename(columns={
        "Date":"datetime",
        "Open":"open",
        "High":"high",
        "Low":"low",
        "Close":"close",
        "Volume":"volume"
    })
    if df.empty:
        return pd.DataFrame()
    df.datetime = pd.DatetimeIndex(df.datetime.dt.date).tz_localize(pytz.utc)
    df = df.drop(columns=["Dividends", "Stock Splits"])
    df = df.set_index("datetime",drop=True)
    return df

def get_histories(tickers, period_starts,period_ends, granularity="1d"):
    dfs = [None]*len(tickers)
    def _helper(i):
        print(tickers[i])
        df = get_history(
            tickers[i],
            period_starts[i], 
            period_ends[i], 
            granularity=granularity
        )
        dfs[i] = df
    threads = [threading.Thread(target=_helper,args=(i,)) for i in range(len(tickers))]
    [thread.start() for thread in threads]
    [thread.join() for thread in threads]
    tickers = [tickers[i] for i in range(len(tickers)) if not dfs[i].empty]
    dfs = [df for df in dfs if not df.empty]
    return tickers, dfs

def get_ticker_dfs(start,end):
    from quantlab.utils import load_pickle,save_pickle
    try:
        tickers, ticker_dfs = load_pickle("dataset.obj")
    except Exception as err:
        tickers = get_sp500_tickers()
        starts=[start]*len(tickers)
        ends=[end]*len(tickers)
        tickers,dfs = get_histories(tickers,starts,ends,granularity="1d")
        ticker_dfs = {ticker:df for ticker,df in zip(tickers,dfs)}
        save_pickle("dataset.obj", (tickers,ticker_dfs))
    return tickers, ticker_dfs 

# class ShortStraddle(LocalSPXData):
    
#     def compute_signals(self,date,capital,trade_range):
#         signal_log = self.strat_buffer[date]
#         notional_leverage = 3
#         notional_per_trade = capital * notional_leverage / np.sum(signal_log["mask"])
#         positions = notional_per_trade / signal_log["underlying"] *-1
#         return {
#             "strategy_pos":positions,
#             "other_underlying":np.zeros(len(positions)),
#             "hedge_pos":np.zeros(len(positions))
#         }
    
#     def compute_metas(self):
#         return 
    
# class RandomStraddle(LocalSSEData):
    
#     def compute_signals(self,date,capital,trade_range):
#         signal_log = self.strat_buffer[date]
#         notional_leverage = 3
#         notional_per_trade = capital * notional_leverage / np.sum(signal_log["mask"])
#         positions = notional_per_trade / signal_log["underlying"] * np.random.choice([-1,1])
#         return {
#             "strategy_pos":positions,
#             "other_underlying":np.zeros(len(positions)),
#             "hedge_pos":np.zeros(len(positions))
#         }
    
#     def compute_metas(self):
#         return 

def custom_option_selector(df,strategy):
    return df

def custom_rebalancer(date,prev_stats,held_contracts,
    held_prices,held_underlying,held_delta,t1_value,
    t1_delta,t1_timestamp,t1_mask,dobj):
    return np.zeros(len(dobj.instruments))

def custom_hedger(capital,date,positions,prev,hedge_stats,dobj):
    return np.zeros(len(dobj.instruments))

from quantlab.gene import Gene, GeneticOptAlpha

async def main():
    trade_start = datetime(2015,1,1,tzinfo=pytz.utc)
    trade_end = datetime(2020,12,30,tzinfo=pytz.utc)

    tickers,ticker_dfs=get_ticker_dfs(start=trade_start,end=trade_end)
    altdata = load_pickle("altdata.obj")
    for ticker,df in altdata.items():
        df["epsDiffZscore"]=(df["epsDifference"]-df["epsDifference"].rolling(12).mean())/df["epsDifference"].rolling(12).std()
    tickers=list(set(tickers).intersection(set(altdata.keys())))
    data_len = {ticker:len(altdata[ticker]) for ticker in tickers}
    data_len = sorted(data_len.items(),key=lambda item:item[1])
    tickers = [item[0] for item in data_len[-100:]]
    
    ticker_dfs = {ticker:ticker_dfs[ticker] for ticker in tickers}
    for ticker in tickers:
        ticker_dfs.update({ticker+"_"+k : v for k,v in altdata[ticker].to_dict(orient="series").items()})

    genome=Gene.str_to_gene("neg(const_1)")
    
    strat = GeneticOptAlpha(
        genome=genome,
        instruments=["SPX"],
        trade_range=(trade_start,trade_end),
        dfs=ticker_dfs,
        strategy={
            "legs":{
                "0":{
                    "type":"call",
                    "selector":[
                        (OPTION_SELECTOR.STRIKE.ATM_REFERENCE_JUMP,0), 
                        (OPTION_SELECTOR.EXPIRY.MIN,7),
                        (OPTION_SELECTOR.EXPIRY.NEAREST,0)
                    ],
                    "contracts":1
                },
                "1":{
                    "type":"call",
                    "selector":[
                        (OPTION_SELECTOR.STRIKE.ATM_REFERENCE_JUMP,0), 
                        (OPTION_SELECTOR.EXPIRY.MIN,7),
                        (OPTION_SELECTOR.EXPIRY.NEAREST,0)
                    ],
                    "contracts":-1
                }
            },
            "underlying":0
        },
        rebalance_triggers=[
            (REBALANCER.ON_DAY_OF_WEEK,0),
        ],
        hedge_triggers=[
            (HEDGE_TRIGGER.ON_DAY_OF_WEEK,4),
            (HEDGE_TRIGGER.DELTA_THRESHOLD,0.3)
        ],
        hedge_target=(HEDGE_TARGET.ZERO,None),
        portfolio_vol=0.10
    )

    df = await strat.run_simulation()
    save_pickle("df.sim",df)
    df.to_csv("df.csv")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())