2025-07-31 17:43:48,519 - __main__ - INFO - test_integration.py:211 - ✅ Logging system initialized
2025-07-31 17:43:48,519 - __main__ - INFO - logging_config.py:144 - [TestContext] Testing contextual logging
2025-07-31 17:43:48,519 - __main__ - INFO - test_integration.py:216 - ✅ Contextual logging working
2025-07-31 17:43:48,519 - __main__ - INFO - test_integration.py:221 - ✅ Log directory created with 3 log files
2025-07-31 17:43:48,519 - __main__ - INFO - test_integration.py:285 - ✅ Logging System: PASSED
2025-07-31 17:43:48,519 - __main__ - INFO - test_integration.py:277 - 
🧪 Running: SPX Downloader Initialization
2025-07-31 17:43:48,519 - __main__ - INFO - test_integration.py:278 - ----------------------------------------
2025-07-31 17:43:48,519 - __main__ - INFO - test_integration.py:234 - Testing SPX downloader initialization...
2025-07-31 17:43:48,523 - __main__ - ERROR - test_integration.py:254 - ❌ SPX downloader initialization failed: No module named 'src'
2025-07-31 17:43:48,523 - __main__ - ERROR - test_integration.py:255 - This may be due to missing configuration or dependencies
2025-07-31 17:43:48,523 - __main__ - ERROR - test_integration.py:287 - ❌ SPX Downloader Initialization: FAILED
2025-07-31 17:43:48,523 - __main__ - INFO - test_integration.py:294 - 
============================================================
2025-07-31 17:43:48,523 - __main__ - INFO - test_integration.py:295 - 🏁 TEST SUMMARY
2025-07-31 17:43:48,523 - __main__ - INFO - test_integration.py:296 - ============================================================
2025-07-31 17:43:48,523 - __main__ - INFO - test_integration.py:303 - ❌ FAIL: Import Tests
2025-07-31 17:43:48,523 - __main__ - INFO - test_integration.py:303 - ✅ PASS: Configuration Tests
2025-07-31 17:43:48,523 - __main__ - INFO - test_integration.py:303 - ❌ FAIL: Data Integration Manager
2025-07-31 17:43:48,523 - __main__ - INFO - test_integration.py:303 - ❌ FAIL: Legacy Compatibility
2025-07-31 17:43:48,523 - __main__ - INFO - test_integration.py:303 - ❌ FAIL: Integrated Data Function
2025-07-31 17:43:48,523 - __main__ - INFO - test_integration.py:303 - ✅ PASS: Logging System
2025-07-31 17:43:48,523 - __main__ - INFO - test_integration.py:303 - ❌ FAIL: SPX Downloader Initialization
2025-07-31 17:43:48,523 - __main__ - INFO - test_integration.py:305 - 
Overall: 2/7 tests passed
2025-07-31 17:43:48,523 - __main__ - WARNING - test_integration.py:311 - ⚠️  5 tests failed. Check the logs above for details.
