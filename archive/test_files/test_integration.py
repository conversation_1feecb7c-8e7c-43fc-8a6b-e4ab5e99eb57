#!/usr/bin/env python3
"""
Integration Test for Optionstrat Data Downloader

This script tests the integration between the data downloader and the main optionstrat program.
"""

import os
import sys
import logging
import pandas as pd
import pytz
from datetime import datetime, timedelta
from pathlib import Path

# Set up basic logging for tests
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_imports():
    """Test that all required modules can be imported."""
    logger.info("Testing imports...")
    
    try:
        # Test basic imports
        import quantlab.utils
        import quantlab.config.config
        logger.info("✅ Basic quantlab imports successful")
        
        # Test data integration imports
        from quantlab.data.data_integration import create_data_manager, DataIntegrationManager
        logger.info("✅ Data integration imports successful")
        
        # Test data downloader imports
        from quantlab.data.data_downloader import SPXDataDownloader
        logger.info("✅ SPX data downloader imports successful")
        
        # Test main program imports
        from main import get_integrated_data, get_ticker_dfs
        logger.info("✅ Main program imports successful")
        
        # Test logging imports
        from quantlab.utils.logging_config import setup_optionstrat_logging
        logger.info("✅ Logging configuration imports successful")
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ Import failed: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected error during imports: {e}")
        return False


def test_configuration():
    """Test configuration loading and validation."""
    logger.info("Testing configuration...")
    
    try:
        from quantlab.config.config import get_config
        
        # Test configuration loading
        config = get_config()
        logger.info("✅ Configuration loaded successfully")
        
        # Test API key presence
        if hasattr(config, 'api_key') and config.api_key:
            logger.info("✅ API key configured")
        else:
            logger.warning("⚠️  API key not configured - SPX options features will be limited")
        
        # Test file paths
        file_paths = config.get_file_paths()
        logger.info(f"✅ File paths configured: {list(file_paths.keys())}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Configuration test failed: {e}")
        return False


def test_data_integration_manager():
    """Test the data integration manager functionality."""
    logger.info("Testing data integration manager...")
    
    try:
        from quantlab.data.data_integration import create_data_manager
        
        # Create data manager
        data_manager = create_data_manager(cache_enabled=True)
        logger.info("✅ Data manager created successfully")
        
        # Test data summary
        summary = data_manager.get_data_summary()
        logger.info(f"✅ Data summary retrieved: {summary}")
        
        # Test with small date range and limited tickers
        start_date = datetime(2023, 1, 1, tzinfo=pytz.utc)
        end_date = datetime(2023, 1, 31, tzinfo=pytz.utc)
        test_tickers = ['AAPL', 'MSFT']
        
        logger.info(f"Testing stock data retrieval for {test_tickers}...")
        valid_tickers, ticker_dfs = data_manager.get_stock_data(
            tickers=test_tickers,
            start_date=start_date,
            end_date=end_date,
            use_cache=True
        )
        
        if valid_tickers and ticker_dfs:
            logger.info(f"✅ Stock data retrieved for {len(valid_tickers)} tickers")
            for ticker in valid_tickers[:2]:  # Show first 2
                df = ticker_dfs[ticker]
                logger.info(f"  {ticker}: {len(df)} records")
        else:
            logger.warning("⚠️  No stock data retrieved")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Data integration manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_legacy_compatibility():
    """Test that legacy functions still work."""
    logger.info("Testing legacy compatibility...")
    
    try:
        from main import get_ticker_dfs, get_sp500_tickers
        
        # Test S&P 500 ticker retrieval
        logger.info("Testing S&P 500 ticker retrieval...")
        tickers = get_sp500_tickers()
        if tickers and len(tickers) > 0:
            logger.info(f"✅ Retrieved {len(tickers)} S&P 500 tickers")
        else:
            logger.warning("⚠️  No S&P 500 tickers retrieved")
        
        # Test legacy data function with very small date range
        start_date = datetime(2023, 6, 1, tzinfo=pytz.utc)
        end_date = datetime(2023, 6, 5, tzinfo=pytz.utc)
        
        logger.info("Testing legacy data retrieval (this may take a moment)...")
        try:
            # This will use cached data if available
            valid_tickers, ticker_dfs = get_ticker_dfs(start_date, end_date)
            if valid_tickers and ticker_dfs:
                logger.info(f"✅ Legacy data retrieval successful: {len(valid_tickers)} tickers")
            else:
                logger.warning("⚠️  Legacy data retrieval returned empty results")
        except Exception as e:
            logger.warning(f"⚠️  Legacy data retrieval failed (may be due to network): {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Legacy compatibility test failed: {e}")
        return False


def test_integrated_data_function():
    """Test the new integrated data function."""
    logger.info("Testing integrated data function...")
    
    try:
        from main import get_integrated_data
        
        # Test with small date range and limited tickers
        start_date = datetime(2023, 1, 1, tzinfo=pytz.utc)
        end_date = datetime(2023, 1, 15, tzinfo=pytz.utc)
        test_tickers = ['AAPL', 'MSFT']
        
        logger.info("Testing integrated data without SPX options...")
        dataset = get_integrated_data(
            start=start_date,
            end=end_date,
            tickers=test_tickers,
            include_spx_options=False,
            use_cache=True
        )
        
        if dataset and 'stock_data' in dataset:
            stock_data = dataset['stock_data']
            logger.info(f"✅ Integrated data retrieved: {len(stock_data['tickers'])} tickers")
        else:
            logger.warning("⚠️  Integrated data function returned unexpected results")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Integrated data function test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_logging_system():
    """Test the logging system."""
    logger.info("Testing logging system...")
    
    try:
        from quantlab.utils.logging_config import setup_optionstrat_logging, get_contextual_logger
        
        # Test logging setup
        root_logger = setup_optionstrat_logging(log_level="INFO")
        logger.info("✅ Logging system initialized")
        
        # Test contextual logger
        ctx_logger = get_contextual_logger(__name__, "TestContext")
        ctx_logger.info("Testing contextual logging")
        logger.info("✅ Contextual logging working")
        
        # Check if log directory was created
        if Path("logs").exists():
            log_files = list(Path("logs").glob("*.log"))
            logger.info(f"✅ Log directory created with {len(log_files)} log files")
        else:
            logger.warning("⚠️  Log directory not created")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Logging system test failed: {e}")
        return False


def test_spx_downloader_initialization():
    """Test SPX downloader initialization (without actually downloading)."""
    logger.info("Testing SPX downloader initialization...")
    
    try:
        from quantlab.data.data_downloader import SPXDataDownloader
        
        # Test initialization
        downloader = SPXDataDownloader()
        logger.info("✅ SPX downloader initialized")
        
        # Test metadata retrieval
        metadata = downloader.get_download_metadata()
        logger.info(f"✅ Metadata retrieved: {metadata}")
        
        # Test file paths
        file_paths = downloader.file_paths
        logger.info(f"✅ File paths configured: {list(file_paths.keys())}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ SPX downloader initialization failed: {e}")
        logger.error("This may be due to missing configuration or dependencies")
        return False


def run_all_tests():
    """Run all integration tests."""
    logger.info("🚀 Starting Optionstrat Integration Tests")
    logger.info("=" * 60)
    
    tests = [
        ("Import Tests", test_imports),
        ("Configuration Tests", test_configuration),
        ("Data Integration Manager", test_data_integration_manager),
        ("Legacy Compatibility", test_legacy_compatibility),
        ("Integrated Data Function", test_integrated_data_function),
        ("Logging System", test_logging_system),
        ("SPX Downloader Initialization", test_spx_downloader_initialization),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running: {test_name}")
        logger.info("-" * 40)
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("🏁 TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{status}: {test_name}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Integration is working correctly.")
        return True
    else:
        logger.warning(f"⚠️  {total - passed} tests failed. Check the logs above for details.")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
