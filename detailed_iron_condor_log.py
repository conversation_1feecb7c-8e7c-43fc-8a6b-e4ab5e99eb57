#!/usr/bin/env python3
"""
Detailed Iron Condor Trade Log

This script generates a comprehensive trade log showing individual option prices,
strikes, P&L calculations, and detailed analysis for each iron condor setup.
"""

import pandas as pd
import numpy as np
from pathlib import Path

def generate_detailed_trade_log():
    """Generate detailed iron condor trade log."""
    
    # Load and prepare data
    data_dir = Path("data")
    master_file = data_dir / "spx_options_24months_optimized.parquet"
    
    if not master_file.exists():
        print("❌ Master data file not found")
        return
    
    df = pd.read_parquet(master_file)
    
    # Apply corrected parsing
    df['contract_type'] = df['contract'].str.extract(r'([CP])(\d{8})')[0]
    df['strike'] = df['contract'].str.extract(r'([CP])(\d{8})')[1].astype(float) / 1000
    df['type'] = df['contract_type'].map({'C': 'call', 'P': 'put'})
    df['last'] = df['close'].fillna((df['bid'] + df['ask']) / 2)
    
    print('📊 DETAILED IRON CONDOR TRADE LOG')
    print('=' * 80)
    
    # Strategy parameters
    wing_width = 20
    put_otm_distance = 0.008
    call_otm_distance = 0.008
    
    # Test dates (last 10 trading days)
    test_dates = ['2025-07-18', '2025-07-21', '2025-07-22', '2025-07-23', '2025-07-24',
                  '2025-07-25', '2025-07-28', '2025-07-29', '2025-07-30', '2025-07-31']
    
    trade_results = []
    
    for i, date in enumerate(test_dates):
        day_data = df[df['date'] == date]
        
        if day_data.empty:
            continue
            
        underlying_price = day_data['spx_price'].iloc[0]
        
        # Calculate target strikes
        short_put_target = underlying_price * (1 - put_otm_distance)
        long_put_target = short_put_target - wing_width
        short_call_target = underlying_price * (1 + call_otm_distance)
        long_call_target = short_call_target + wing_width
        
        # Find legs
        puts = day_data[day_data['type'] == 'put']
        calls = day_data[day_data['type'] == 'call']
        
        tolerance = 15
        
        short_put_candidates = puts[abs(puts['strike'] - short_put_target) <= tolerance]
        long_put_candidates = puts[abs(puts['strike'] - long_put_target) <= tolerance]
        short_call_candidates = calls[abs(calls['strike'] - short_call_target) <= tolerance]
        long_call_candidates = calls[abs(calls['strike'] - long_call_target) <= tolerance]
        
        if (len(short_put_candidates) > 0 and len(long_put_candidates) > 0 and 
            len(short_call_candidates) > 0 and len(long_call_candidates) > 0):
            
            # Select best strikes (closest to target)
            short_put = short_put_candidates.iloc[0]
            long_put = long_put_candidates.iloc[0]
            short_call = short_call_candidates.iloc[0]
            long_call = long_call_candidates.iloc[0]
            
            # Calculate credit
            credit = short_put['last'] + short_call['last'] - long_put['last'] - long_call['last']
            
            # Calculate breakevens
            lower_breakeven = short_put['strike'] - credit
            upper_breakeven = short_call['strike'] + credit
            
            # Calculate max profit/loss
            max_profit = credit
            max_loss = wing_width - credit
            
            print(f'\n🔸 TRADE #{i+1} - {date}')
            print(f'   SPX Price: ${underlying_price:.2f}')
            print(f'   ┌─────────────────────────────────────────────────────────┐')
            print(f'   │ LEG DETAILS                                             │')
            print(f'   ├─────────────────────────────────────────────────────────┤')
            print(f'   │ Short Put:  SELL {short_put["strike"]:.0f}P @ ${short_put["last"]:.2f}     │')
            print(f'   │ Long Put:   BUY  {long_put["strike"]:.0f}P @ ${long_put["last"]:.2f}     │')
            print(f'   │ Short Call: SELL {short_call["strike"]:.0f}C @ ${short_call["last"]:.2f}    │')
            print(f'   │ Long Call:  BUY  {long_call["strike"]:.0f}C @ ${long_call["last"]:.2f}     │')
            print(f'   ├─────────────────────────────────────────────────────────┤')
            print(f'   │ NET CREDIT: ${credit:.2f}                               │')
            print(f'   │ MAX PROFIT: ${max_profit:.2f}                           │')
            print(f'   │ MAX LOSS:   ${max_loss:.2f}                             │')
            print(f'   │ BREAKEVENS: ${lower_breakeven:.2f} - ${upper_breakeven:.2f}        │')
            print(f'   │ PROFIT ZONE: {upper_breakeven - lower_breakeven:.1f} points         │')
            print(f'   └─────────────────────────────────────────────────────────┘')
            
            # P&L at current price
            if lower_breakeven <= underlying_price <= upper_breakeven:
                pnl_status = '✅ PROFITABLE'
                current_pnl = credit
            else:
                pnl_status = '❌ LOSING'
                if underlying_price < lower_breakeven:
                    current_pnl = credit - (lower_breakeven - underlying_price)
                else:
                    current_pnl = credit - (underlying_price - upper_breakeven)
            
            print(f'   📊 P&L at Current Price: ${current_pnl:.2f} ({pnl_status})')
            
            # Store trade results
            trade_results.append({
                'trade_num': i+1,
                'date': date,
                'spx_price': underlying_price,
                'short_put_strike': short_put['strike'],
                'short_put_price': short_put['last'],
                'long_put_strike': long_put['strike'],
                'long_put_price': long_put['last'],
                'short_call_strike': short_call['strike'],
                'short_call_price': short_call['last'],
                'long_call_strike': long_call['strike'],
                'long_call_price': long_call['last'],
                'credit': credit,
                'max_profit': max_profit,
                'max_loss': max_loss,
                'lower_breakeven': lower_breakeven,
                'upper_breakeven': upper_breakeven,
                'profit_zone_width': upper_breakeven - lower_breakeven,
                'current_pnl': current_pnl,
                'profitable': current_pnl > 0
            })
    
    # Generate summary statistics
    if trade_results:
        results_df = pd.DataFrame(trade_results)
        
        print(f'\n' + '=' * 80)
        print(f'📈 COMPREHENSIVE P&L REPORT')
        print(f'=' * 80)
        
        total_trades = len(results_df)
        profitable_trades = len(results_df[results_df['profitable']])
        total_credit = results_df['credit'].sum()
        total_pnl = results_df['current_pnl'].sum()
        avg_profit_zone = results_df['profit_zone_width'].mean()
        
        print(f'📊 PERFORMANCE SUMMARY:')
        print(f'   Total Trades: {total_trades}')
        print(f'   Profitable Trades: {profitable_trades} ({profitable_trades/total_trades*100:.1f}%)')
        print(f'   Losing Trades: {total_trades - profitable_trades} ({(total_trades-profitable_trades)/total_trades*100:.1f}%)')
        print(f'   Total Credit Collected: ${total_credit:.2f}')
        print(f'   Total P&L: ${total_pnl:.2f}')
        print(f'   Average P&L per Trade: ${total_pnl/total_trades:.2f}')
        print(f'   Best Trade: ${results_df["current_pnl"].max():.2f}')
        print(f'   Worst Trade: ${results_df["current_pnl"].min():.2f}')
        print(f'   Average Profit Zone: {avg_profit_zone:.1f} points')
        
        print(f'\n💰 RISK METRICS:')
        avg_max_profit = results_df['max_profit'].mean()
        avg_max_loss = results_df['max_loss'].mean()
        print(f'   Average Max Profit: ${avg_max_profit:.2f}')
        print(f'   Average Max Loss: ${avg_max_loss:.2f}')
        print(f'   Risk/Reward Ratio: {avg_max_profit/avg_max_loss:.2f}')
        print(f'   Win Rate Required for Breakeven: {avg_max_loss/(avg_max_profit + avg_max_loss)*100:.1f}%')
        
        print(f'\n🎯 STRIKE ANALYSIS:')
        print(f'   Short Put Strike Range: {results_df["short_put_strike"].min():.0f} - {results_df["short_put_strike"].max():.0f}')
        print(f'   Short Call Strike Range: {results_df["short_call_strike"].min():.0f} - {results_df["short_call_strike"].max():.0f}')
        print(f'   Average Wing Width: {(results_df["short_call_strike"] - results_df["short_put_strike"]).mean():.1f} points')
        
        print(f'\n📋 STRATEGY PARAMETERS:')
        print(f'   Wing Width: {wing_width} points')
        print(f'   Put OTM Distance: {put_otm_distance*100:.1f}%')
        print(f'   Call OTM Distance: {call_otm_distance*100:.1f}%')
        print(f'   Market Range: ${df["spx_price"].min():.0f} - ${df["spx_price"].max():.0f}')
        print(f'   Data Period: {df["date"].min()} to {df["date"].max()}')
        print(f'   Total Records: {len(df):,}')
        
        return results_df
    
    return None

if __name__ == "__main__":
    results = generate_detailed_trade_log()
    if results is not None:
        print(f'\n✅ Generated detailed trade log for {len(results)} iron condor setups')
    else:
        print(f'\n❌ No trade setups found')
