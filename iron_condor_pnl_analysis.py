#!/usr/bin/env python3
"""
Iron Condor P&L Analysis with SPX Options Data

This script analyzes the profit and loss characteristics of iron condor strategies
using our comprehensive SPX options dataset.
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
from pathlib import Path
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_spx_options_data():
    """Load SPX options data for iron condor analysis."""
    
    data_dir = Path("data")
    
    # Try to load the master file first (faster)
    master_file = data_dir / "spx_options_24months_optimized.parquet"
    
    if master_file.exists():
        try:
            df = pd.read_parquet(master_file)
            logger.info(f"✅ Loaded master file: {len(df):,} records")
            return df
        except Exception as e:
            logger.warning(f"⚠️  Failed to load master file: {e}")
    
    # Fallback to individual parquet files
    parquet_files = list(data_dir.glob("spx_options_*.parquet"))
    
    if not parquet_files:
        logger.error("❌ No SPX options parquet files found!")
        return None
    
    # Load recent files for analysis
    dfs = []
    for pf in sorted(parquet_files)[-50:]:  # Last 50 days
        try:
            df = pd.read_parquet(pf)
            if not df.empty:
                dfs.append(df)
        except Exception as e:
            logger.warning(f"⚠️  Failed to load {pf.name}: {e}")
    
    if not dfs:
        return None
    
    combined_df = pd.concat(dfs, ignore_index=True)
    logger.info(f"✅ Loaded combined data: {len(combined_df):,} records")
    
    return combined_df

def prepare_options_data(df):
    """Prepare options data for iron condor analysis."""
    
    logger.info("🔄 Preparing options data for iron condor analysis...")
    
    # Parse contract information
    df['contract_type'] = df['contract'].str.extract(r'([CP])')[0].map({'C': 'call', 'P': 'put'})
    df['strike'] = df['contract'].str.extract(r'([CP])(\d{8})')[1].astype(float) / 1000
    df['moneyness'] = df['strike'] / df['spx_price']
    
    # Extract expiration info
    exp_str = df['contract'].str.extract(r'SPX[W]?(\d{6})[CP]')[0]
    df['expiration'] = pd.to_datetime('20' + exp_str, format='%Y%m%d')
    df['dte'] = (df['expiration'] - pd.to_datetime(df['date'])).dt.days
    
    # Convert datetime for time analysis (handle string format)
    try:
        df['datetime_parsed'] = pd.to_datetime(df['datetime'])
        df['time_est'] = df['datetime_parsed'].dt.strftime('%H:%M:%S')
    except:
        # If datetime is already a string, extract time portion
        df['time_est'] = df['datetime'].str.extract(r'(\d{2}:\d{2}:\d{2})')[0]
    
    logger.info(f"✅ Prepared data with {len(df):,} records")
    logger.info(f"   Calls: {len(df[df['contract_type'] == 'call']):,}")
    logger.info(f"   Puts: {len(df[df['contract_type'] == 'put']):,}")
    logger.info(f"   DTE Range: {df['dte'].min()} - {df['dte'].max()} days")
    
    return df

def find_iron_condor_legs(df, target_date, target_dte=30, wing_width=50):
    """Find the four legs of an iron condor for a specific date."""
    
    # Filter for target date and DTE
    day_data = df[
        (df['date'] == target_date) & 
        (df['dte'] >= target_dte - 2) & 
        (df['dte'] <= target_dte + 2)
    ].copy()
    
    if day_data.empty:
        return None
    
    current_spx = day_data['spx_price'].iloc[0]
    
    # Define iron condor strikes
    # Short put: 5% OTM (0.95 moneyness)
    # Long put: Further OTM (0.95 - wing_width/current_spx)
    # Short call: 5% OTM (1.05 moneyness)  
    # Long call: Further OTM (1.05 + wing_width/current_spx)
    
    short_put_target = current_spx * 0.95
    long_put_target = short_put_target - wing_width
    short_call_target = current_spx * 1.05
    long_call_target = short_call_target + wing_width
    
    # Find closest strikes for each leg
    puts = day_data[day_data['contract_type'] == 'put']
    calls = day_data[day_data['contract_type'] == 'call']
    
    # Short put (sell)
    short_put = puts.loc[(puts['strike'] - short_put_target).abs().idxmin()] if not puts.empty else None
    
    # Long put (buy) 
    long_put_candidates = puts[puts['strike'] < short_put['strike']] if short_put is not None else pd.DataFrame()
    long_put = long_put_candidates.loc[(long_put_candidates['strike'] - long_put_target).abs().idxmin()] if not long_put_candidates.empty else None
    
    # Short call (sell)
    short_call = calls.loc[(calls['strike'] - short_call_target).abs().idxmin()] if not calls.empty else None
    
    # Long call (buy)
    long_call_candidates = calls[calls['strike'] > short_call['strike']] if short_call is not None else pd.DataFrame()
    long_call = long_call_candidates.loc[(long_call_candidates['strike'] - long_call_target).abs().idxmin()] if not long_call_candidates.empty else None
    
    if any(leg is None for leg in [short_put, long_put, short_call, long_call]):
        return None
    
    return {
        'date': target_date,
        'spx_price': current_spx,
        'short_put': short_put,
        'long_put': long_put,
        'short_call': short_call,
        'long_call': long_call,
        'dte': short_put['dte']
    }

def calculate_iron_condor_pnl(condor_legs, expiration_prices):
    """Calculate P&L for iron condor at various expiration prices."""
    
    if not condor_legs:
        return None
    
    # Extract strikes and premiums
    short_put_strike = condor_legs['short_put']['strike']
    long_put_strike = condor_legs['long_put']['strike']
    short_call_strike = condor_legs['short_call']['strike']
    long_call_strike = condor_legs['long_call']['strike']
    
    # Net credit received (premium collected - premium paid)
    credit_received = (
        condor_legs['short_put']['close'] +  # Sell put
        condor_legs['short_call']['close'] -  # Sell call
        condor_legs['long_put']['close'] -   # Buy put
        condor_legs['long_call']['close']    # Buy call
    )
    
    pnl_results = []
    
    for exp_price in expiration_prices:
        # Put spread P&L
        if exp_price <= long_put_strike:
            # Max loss on put spread
            put_pnl = -(short_put_strike - long_put_strike)
        elif exp_price >= short_put_strike:
            # No loss on put spread
            put_pnl = 0
        else:
            # Partial loss on put spread
            put_pnl = -(short_put_strike - exp_price)
        
        # Call spread P&L
        if exp_price >= long_call_strike:
            # Max loss on call spread
            call_pnl = -(long_call_strike - short_call_strike)
        elif exp_price <= short_call_strike:
            # No loss on call spread
            call_pnl = 0
        else:
            # Partial loss on call spread
            call_pnl = -(exp_price - short_call_strike)
        
        # Total P&L = Credit received + Put P&L + Call P&L
        total_pnl = credit_received + put_pnl + call_pnl
        
        pnl_results.append({
            'expiration_price': exp_price,
            'put_pnl': put_pnl,
            'call_pnl': call_pnl,
            'total_pnl': total_pnl,
            'credit_received': credit_received
        })
    
    return pd.DataFrame(pnl_results)

def analyze_iron_condor_performance(df):
    """Analyze iron condor performance across multiple dates."""
    
    logger.info("📊 Analyzing iron condor performance...")
    
    # Get unique dates for analysis
    unique_dates = sorted(df['date'].unique())
    
    # Analyze iron condors for recent dates
    analysis_dates = unique_dates[-20:]  # Last 20 trading days
    
    condor_results = []
    
    for date in analysis_dates:
        logger.info(f"   Analyzing iron condor for {date}")
        
        # Find iron condor legs
        condor_legs = find_iron_condor_legs(df, date, target_dte=30, wing_width=50)
        
        if not condor_legs:
            continue
        
        # Calculate P&L profile
        current_spx = condor_legs['spx_price']
        price_range = np.arange(current_spx * 0.85, current_spx * 1.15, 5)
        
        pnl_profile = calculate_iron_condor_pnl(condor_legs, price_range)
        
        if pnl_profile is not None:
            # Calculate key metrics
            max_profit = pnl_profile['total_pnl'].max()
            max_loss = pnl_profile['total_pnl'].min()
            breakeven_points = pnl_profile[abs(pnl_profile['total_pnl']) < 0.5]['expiration_price'].tolist()
            
            condor_results.append({
                'date': date,
                'spx_price': current_spx,
                'credit_received': condor_legs['short_put']['close'] + condor_legs['short_call']['close'] - 
                                 condor_legs['long_put']['close'] - condor_legs['long_call']['close'],
                'max_profit': max_profit,
                'max_loss': max_loss,
                'profit_loss_ratio': abs(max_profit / max_loss) if max_loss != 0 else 0,
                'short_put_strike': condor_legs['short_put']['strike'],
                'short_call_strike': condor_legs['short_call']['strike'],
                'wing_width': condor_legs['short_call']['strike'] - condor_legs['short_put']['strike'],
                'dte': condor_legs['dte'],
                'breakeven_points': len(breakeven_points)
            })
    
    return pd.DataFrame(condor_results)

def plot_iron_condor_pnl(condor_legs, save_plot=True):
    """Plot iron condor P&L diagram."""
    
    if not condor_legs:
        return
    
    current_spx = condor_legs['spx_price']
    price_range = np.arange(current_spx * 0.85, current_spx * 1.15, 1)
    
    pnl_profile = calculate_iron_condor_pnl(condor_legs, price_range)
    
    if pnl_profile is None:
        return
    
    plt.figure(figsize=(12, 8))
    
    # Plot P&L curve
    plt.plot(pnl_profile['expiration_price'], pnl_profile['total_pnl'], 
             linewidth=3, color='blue', label='Total P&L')
    
    # Mark current SPX price
    plt.axvline(x=current_spx, color='red', linestyle='--', alpha=0.7, label=f'Current SPX: ${current_spx:.0f}')
    
    # Mark strike prices
    plt.axvline(x=condor_legs['short_put']['strike'], color='green', linestyle=':', alpha=0.7, 
                label=f'Short Put: ${condor_legs["short_put"]["strike"]:.0f}')
    plt.axvline(x=condor_legs['short_call']['strike'], color='orange', linestyle=':', alpha=0.7,
                label=f'Short Call: ${condor_legs["short_call"]["strike"]:.0f}')
    
    # Mark breakeven points
    breakeven_mask = abs(pnl_profile['total_pnl']) < 0.5
    breakeven_points = pnl_profile[breakeven_mask]['expiration_price']
    for be in breakeven_points:
        plt.axvline(x=be, color='purple', linestyle='-.', alpha=0.5)
    
    # Add profit/loss zones
    plt.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    plt.fill_between(pnl_profile['expiration_price'], pnl_profile['total_pnl'], 0, 
                     where=(pnl_profile['total_pnl'] > 0), alpha=0.3, color='green', label='Profit Zone')
    plt.fill_between(pnl_profile['expiration_price'], pnl_profile['total_pnl'], 0,
                     where=(pnl_profile['total_pnl'] < 0), alpha=0.3, color='red', label='Loss Zone')
    
    plt.xlabel('SPX Price at Expiration')
    plt.ylabel('Profit/Loss ($)')
    plt.title(f'Iron Condor P&L - {condor_legs["date"]} (DTE: {condor_legs["dte"]})')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    if save_plot:
        plt.savefig(f'iron_condor_pnl_{condor_legs["date"]}.png', dpi=300, bbox_inches='tight')
        logger.info(f"💾 Saved P&L chart: iron_condor_pnl_{condor_legs['date']}.png")
    
    plt.show()

def main():
    """Main iron condor P&L analysis."""
    
    print("📊 Iron Condor P&L Analysis with SPX Options Data")
    print("=" * 60)
    
    # Load SPX options data
    spx_data = load_spx_options_data()
    
    if spx_data is None:
        print("❌ No SPX options data available")
        return False
    
    # Prepare data
    prepared_data = prepare_options_data(spx_data)
    
    # Analyze iron condor performance
    condor_performance = analyze_iron_condor_performance(prepared_data)
    
    if condor_performance.empty:
        print("❌ No iron condor data found")
        return False
    
    # Display results
    print(f"\n📈 IRON CONDOR PERFORMANCE ANALYSIS")
    print("=" * 45)
    print(f"Analyzed {len(condor_performance)} iron condor setups")
    
    print(f"\n💰 Profitability Metrics:")
    print(f"   Average Credit Received: ${condor_performance['credit_received'].mean():.2f}")
    print(f"   Average Max Profit: ${condor_performance['max_profit'].mean():.2f}")
    print(f"   Average Max Loss: ${condor_performance['max_loss'].mean():.2f}")
    print(f"   Average P/L Ratio: {condor_performance['profit_loss_ratio'].mean():.2f}")
    
    print(f"\n📊 Strategy Characteristics:")
    print(f"   Average Wing Width: ${condor_performance['wing_width'].mean():.0f}")
    print(f"   Average DTE: {condor_performance['dte'].mean():.0f} days")
    print(f"   Credit Range: ${condor_performance['credit_received'].min():.2f} - ${condor_performance['credit_received'].max():.2f}")
    
    # Show detailed results
    print(f"\n📋 Recent Iron Condor Results:")
    display_cols = ['date', 'spx_price', 'credit_received', 'max_profit', 'max_loss', 'profit_loss_ratio']
    print(condor_performance[display_cols].tail(10).to_string(index=False, float_format='%.2f'))
    
    # Plot example iron condor
    if not condor_performance.empty:
        latest_date = condor_performance['date'].iloc[-1]
        example_condor = find_iron_condor_legs(prepared_data, latest_date, target_dte=30, wing_width=50)
        
        if example_condor:
            print(f"\n📊 Plotting example iron condor for {latest_date}")
            plot_iron_condor_pnl(example_condor)
    
    # Save results
    results_file = f"iron_condor_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    condor_performance.to_csv(results_file, index=False)
    logger.info(f"💾 Results saved to: {results_file}")
    
    print(f"\n🎉 Iron Condor P&L Analysis Complete!")
    print(f"📁 Results saved to: {results_file}")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
