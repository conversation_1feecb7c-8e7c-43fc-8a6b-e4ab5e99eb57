# Optionstrat Program - Integrated Data Downloader

A comprehensive options trading system with integrated data sources, including stock data via yfinance and SPX options data via Polygon.io API. The system supports genetic algorithm-based strategy development and backtesting.

## 🚀 Features

### Data Integration
- **Multi-Source Data**: Seamlessly integrates stock data (yfinance) and SPX options data (Polygon.io)
- **Intelligent Caching**: Automatic data caching with validation for faster subsequent runs
- **Flexible Data Sources**: Choose between legacy yfinance-only or integrated data approaches
- **Error Handling**: Robust error handling with automatic fallback mechanisms

### SPX Options Data
- **Real-Time Download**: Automated download of SPX options data with 5-minute intraday bars
- **Greeks Calculation**: Real-time calculation of option Greeks (Delta, Gamma, Theta, Vega, Rho)
- **Historical Data**: Support for both full and incremental data downloads
- **Contract Management**: Automatic options chain discovery and contract filtering

### Strategy Development
- **Genetic Algorithms**: Advanced genetic algorithm framework for strategy development
- **Multiple Strategies**: Support for various trading strategies and signal generation
- **Backtesting**: Comprehensive backtesting with performance analytics
- **Risk Management**: Built-in risk controls and position sizing

### System Features
- **Modular Architecture**: Clean, extensible codebase with proper separation of concerns
- **Comprehensive Logging**: Detailed logging with contextual information and file rotation
- **Configuration Management**: Environment-based configuration with .env support
- **Command-Line Interface**: Easy-to-use CLI with multiple options

## 📋 Prerequisites

- Python 3.8+
- Polygon.io API key (for SPX options data)
- Internet connection for data downloads

## 🛠️ Installation

1. **Clone the repository:**
```bash
git clone <repository-url>
cd optionstrat
```

2. **Install dependencies:**
```bash
pip install -r requirements.txt
```

3. **Set up environment variables:**
```bash
# The .env file is already configured with sample values
# Update the POLYGON_API_KEY with your actual API key
```

## 🚀 Quick Start

### Basic Usage

```bash
# Run with integrated data (default)
python main.py

# Run with legacy yfinance-only data
python main.py --legacy-data

# Include SPX options data
python main.py --include-spx-options

# Show data source summary
python main.py --data-summary
```

### Python API Usage

```python
from quantlab.data.data_integration import create_data_manager
from datetime import datetime
import pytz

# Create data manager
data_manager = create_data_manager()

# Get stock data
start_date = datetime(2023, 1, 1, tzinfo=pytz.utc)
end_date = datetime(2023, 6, 30, tzinfo=pytz.utc)
tickers = ['AAPL', 'MSFT', 'GOOGL']

valid_tickers, ticker_dfs = data_manager.get_stock_data(tickers, start_date, end_date)

# Get combined dataset with SPX options
dataset = data_manager.get_combined_dataset(
    tickers=tickers,
    start_date=start_date,
    end_date=end_date,
    include_spx_options=True
)
```

## 📊 Data Sources

### Stock Data (yfinance)
- **Source**: Yahoo Finance via yfinance library
- **Coverage**: S&P 500 stocks and custom ticker lists
- **Frequency**: Daily data with OHLCV
- **Caching**: Automatic caching with pickle compression

### SPX Options Data (Polygon.io)
- **Source**: Polygon.io API
- **Coverage**: SPX options contracts with historical data
- **Frequency**: 5-minute intraday bars
- **Features**: Bid/ask spreads, Greeks calculation, contract metadata
- **Storage**: Parquet format with automatic compression

## ⚙️ Configuration

The system uses environment variables for configuration. Key settings in `.env`:

```bash
# API Configuration
POLYGON_API_KEY=your_api_key_here

# Data Directories
DATA_DIR=./data
REPORTS_DIR=./reports
LOGS_DIR=./logs

# Download Settings
DOWNLOAD_MONTHS=16
ENABLE_DATA_INTEGRATION=true
ENABLE_SPX_OPTIONS=false
DEFAULT_CACHE_ENABLED=true
```

## 🏗️ Architecture

### Core Components

1. **Data Integration Layer** (`quantlab/data/`)
   - `data_integration.py`: Unified data manager
   - `data_downloader.py`: SPX options downloader
   - Handles caching, error recovery, and data validation

2. **Configuration Layer** (`quantlab/config/`)
   - `config.py`: Centralized configuration management
   - Environment variable integration
   - Validation and defaults

3. **Utilities Layer** (`quantlab/utils/`)
   - `logging_config.py`: Comprehensive logging setup
   - `utils.py`: Core utilities and portfolio management
   - Caching and serialization helpers

4. **Strategy Layer** (`quantlab/`)
   - `gene.py`: Genetic algorithm framework
   - Strategy development and backtesting
   - Performance analytics

### Data Flow

```
Environment Config → Data Manager → [Stock Data + SPX Options] → Strategy Engine → Results
                                      ↓
                                   Cache Layer
```

## 📝 Usage Examples

### Example 1: Basic Stock Data Analysis
```python
from main import get_integrated_data
from datetime import datetime
import pytz

# Get stock data for analysis
start_date = datetime(2023, 1, 1, tzinfo=pytz.utc)
end_date = datetime(2023, 6, 30, tzinfo=pytz.utc)

dataset = get_integrated_data(
    start=start_date,
    end=end_date,
    tickers=['AAPL', 'MSFT', 'GOOGL'],
    include_spx_options=False
)

stock_data = dataset['stock_data']
print(f"Retrieved data for {len(stock_data['tickers'])} tickers")
```

### Example 2: SPX Options Analysis
```python
# Include SPX options data
dataset = get_integrated_data(
    start=start_date,
    end=end_date,
    include_spx_options=True,
    spx_download_mode="incremental"
)

spx_data = dataset['spx_options_data']
if spx_data is not None:
    print(f"SPX options: {len(spx_data)} records")
    print(f"Unique contracts: {spx_data['contract'].nunique()}")
```

### Example 3: Running Genetic Algorithm Strategies
```python
# Run the main program with different configurations
from main import main

# Run with integrated data and SPX options
main(use_integrated_data=True, include_spx_options=True)

# Run with legacy data only
main(use_integrated_data=False, include_spx_options=False)
```

## 🔧 Command Line Options

```bash
# Show all available options
python main.py --help

# Get data source summary
python main.py --data-summary

# Use legacy data approach
python main.py --legacy-data

# Include SPX options data
python main.py --include-spx-options
```

## 📊 Logging and Monitoring

The system provides comprehensive logging:

- **Main Log**: `logs/optionstrat_YYYYMMDD.log` - All application events
- **Error Log**: `logs/optionstrat_errors_YYYYMMDD.log` - Errors and critical issues
- **Data Log**: `logs/data_downloader_YYYYMMDD.log` - Data download operations

## 🚨 Error Handling

The system includes robust error handling:

1. **Automatic Fallback**: If integrated data fails, falls back to legacy yfinance
2. **Retry Logic**: Automatic retries for network operations
3. **Data Validation**: Validates data integrity before processing
4. **Graceful Degradation**: Continues operation even if some data sources fail

## 🔄 Data Caching

- **Automatic Caching**: Stock data is automatically cached for faster access
- **Cache Validation**: Checks if cached data covers requested date ranges
- **Cache Management**: Automatic cleanup and refresh of stale data

## 📈 Performance

- **Parallel Downloads**: Multi-threaded data downloads for faster processing
- **Efficient Storage**: Uses Parquet format for optimal storage and loading
- **Memory Management**: Efficient memory usage for large datasets

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues and questions:
1. Check the logs in the `logs/` directory
2. Run `python main.py --data-summary` to check system status
3. Review the example usage in `example_usage.py`
4. Check the configuration in `.env`

## 🔮 Future Enhancements

- Additional data sources integration
- Real-time data streaming
- Advanced strategy optimization
- Web-based dashboard
- Cloud deployment support
