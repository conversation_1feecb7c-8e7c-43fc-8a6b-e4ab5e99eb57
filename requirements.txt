# SPX Options Trading System - Production Requirements
# Modular system with OI skew filtering and advanced risk management

# Core Data Processing
pandas>=2.0.0
numpy>=1.24.0
scipy>=1.11.0

# Data Storage
pyarrow>=12.0.0

# API and Web Requests
requests>=2.31.0

# PDF Report Generation
reportlab>=4.0.0

# Chart Generation
matplotlib>=3.7.0

# Environment Variables
python-dotenv>=1.0.0

# Date and Time Handling
python-dateutil>=2.8.0
pytz>=2023.3

# Additional dependencies for data downloader
beautifulsoup4>=4.12.0
yfinance>=0.2.0
dill>=0.3.7

# Development and Testing (Optional)
pytest>=7.4.0
pytest-cov>=4.1.0

# Optional: Advanced Analytics
# scipy>=1.11.0
# scikit-learn>=1.3.0
# matplotlib>=3.7.0
# seaborn>=0.12.0

# Optional: Jupyter for Analysis
# jupyter>=1.0.0
# ipykernel>=6.25.0
