#!/usr/bin/env python3
"""
Run OptAlpha with SPX Options Data

This script demonstrates how to use the OptAlpha framework with our 5+ million
SPX options records to create and backtest options trading strategies.
"""

import os
import sys
import pandas as pd
import numpy as np
import pytz
from datetime import datetime, timedelta
from pathlib import Path
import logging

# Import OptAlpha framework
from quantlab.optalpha import OptAlpha, OPTION_SELECTOR, REBALANCER, HEDGE_TRIGGER, HEDGE_TARGET

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SPXOptAlpha(OptAlpha):
    """
    SPX Options Alpha implementation using our downloaded SPX options data.
    """
    
    def __init__(self, spx_data, instruments=None, **kwargs):
        """
        Initialize with SPX options data.

        Args:
            spx_data: DataFrame with SPX options data
            instruments: List of instruments (default: ['SPX'])
            **kwargs: Additional arguments for OptAlpha
        """
        self.spx_data = spx_data
        self.prepared_data = None

        # Default instruments (SPX only for now)
        if instruments is None:
            instruments = ['SPX']

        super().__init__(instruments=instruments, **kwargs)
        
    def prepare_spx_data(self):
        """Prepare SPX options data for OptAlpha framework."""
        
        logger.info("🔄 Preparing SPX options data for OptAlpha...")
        
        # Convert our SPX data to the format expected by OptAlpha
        df = self.spx_data.copy()
        
        # Parse contract information
        df['optionroot'] = df['contract']
        df['underlying'] = 'SPX'
        df['underlying_last'] = df['spx_price']
        
        # Extract option type and strike from contract
        # Contract format: O:SPXW250806C06390000
        df['type'] = df['contract'].str.extract(r'([CP])')[0].map({'C': 'call', 'P': 'put'})
        df['strike'] = df['contract'].str.extract(r'([CP])(\d{8})')[1].astype(float) / 1000
        
        # Use the date column directly for quotedate
        df['quotedate'] = pd.to_datetime(df['date'])

        # Calculate days to expiration (DTE)
        # Extract expiration date from contract (YYMMDD format)
        exp_str = df['contract'].str.extract(r'SPX[W]?(\d{6})[CP]')[0]
        df['expiration'] = pd.to_datetime('20' + exp_str, format='%Y%m%d')

        # Calculate DTE
        df['dte'] = (df['expiration'] - df['quotedate']).dt.days
        
        # Use close price as last price
        df['last'] = df['close']
        
        # Add volume and open interest (use bid_size + ask_size as proxy for volume)
        df['volume'] = df['bid_size'] + df['ask_size']
        df['openinterest'] = df['volume'] * 10  # Rough estimate
        
        # Select required columns
        required_cols = [
            'optionroot', 'underlying', 'underlying_last', 'type',
            'quotedate', 'strike', 'last', 'volume', 'openinterest', 'dte'
        ]
        
        self.prepared_data = df[required_cols].copy()
        
        logger.info(f"✅ Prepared {len(self.prepared_data):,} SPX options records")
        logger.info(f"   Date range: {df['quotedate'].min()} to {df['quotedate'].max()}")
        logger.info(f"   DTE range: {df['dte'].min()} to {df['dte'].max()} days")
        logger.info(f"   Strike range: ${df['strike'].min():.0f} to ${df['strike'].max():.0f}")
        
        return self.prepared_data
    
    def instantiate_variables(self):
        """Initialize variables for the simulation."""
        logger.info("🔧 Initializing OptAlpha variables...")
        
    def load_buffer(self, load_from, test_end=None, min_buffer_len=100, min_hist_len=2):
        """Load data buffer for the given date."""
        
        if self.prepared_data is None:
            self.prepare_spx_data()
        
        # Filter data for the requested date range
        start_date = pd.Timestamp(load_from - timedelta(days=min_buffer_len))
        end_date = pd.Timestamp(test_end if test_end else load_from + timedelta(days=30))

        # Get data for the date range
        mask = (
            (self.prepared_data['quotedate'] >= start_date) &
            (self.prepared_data['quotedate'] <= end_date)
        )
        
        buffer_data = self.prepared_data[mask].copy()
        
        if len(buffer_data) == 0:
            logger.warning(f"⚠️  No data found for date range {start_date} to {end_date}")
            return
        
        # Group by date and add to buffer
        for date, day_data in buffer_data.groupby(buffer_data['quotedate'].dt.date):
            date_ts = pd.Timestamp(date, tz=pytz.utc)
            if date_ts not in self.data_buffer_idx:
                self.data_buffer.append(day_data)
                self.data_buffer_idx.append(date_ts)
        
        logger.info(f"📊 Loaded buffer: {len(buffer_data):,} records across {len(set(buffer_data['quotedate'].dt.date))} days")
    
    def compute_signals(self, date, capital):
        """Compute trading signals for the given date."""
        
        # Simple strategy: hold ATM straddles
        strategy_pos = np.ones(len(self.instruments))  # 1 contract per instrument
        other_underlying = np.zeros(len(self.instruments))  # No direct underlying exposure
        
        return {
            'strategy_pos': strategy_pos,
            'other_underlying': other_underlying
        }
    
    def compute_metas(self, trade_range):
        """Compute metadata for the trading range."""
        logger.info(f"📈 Computing metadata for {len(trade_range)} trading days")

def load_spx_options_data():
    """Load all available SPX options parquet files."""
    
    data_dir = Path("data")
    parquet_files = list(data_dir.glob("spx_options_*.parquet"))
    
    if not parquet_files:
        logger.error("❌ No SPX options parquet files found!")
        logger.info("💡 Make sure you have downloaded SPX options data first")
        return None
    
    logger.info(f"📁 Found {len(parquet_files)} SPX options files")
    
    # Load and combine all parquet files (limit to recent data for performance)
    dfs = []
    total_records = 0
    
    # Sort files and take the most recent ones for faster processing
    sorted_files = sorted(parquet_files)[-100:]  # Last 100 days
    
    for pf in sorted_files:
        try:
            df = pd.read_parquet(pf)
            if not df.empty:
                dfs.append(df)
                total_records += len(df)
        except Exception as e:
            logger.warning(f"  ⚠️  Failed to load {pf.name}: {e}")
    
    if not dfs:
        logger.error("❌ No valid parquet files could be loaded")
        return None
    
    # Combine all dataframes
    logger.info("🔄 Combining SPX options data...")
    combined_df = pd.concat(dfs, ignore_index=True)
    
    # Sort by date and datetime
    combined_df = combined_df.sort_values(['date', 'datetime']).reset_index(drop=True)
    
    logger.info(f"✅ Combined SPX options data:")
    logger.info(f"   Total records: {len(combined_df):,}")
    logger.info(f"   Date range: {combined_df['date'].min()} to {combined_df['date'].max()}")
    logger.info(f"   Unique contracts: {combined_df['contract'].nunique():,}")
    
    return combined_df

def create_sample_strategy():
    """Create a sample options trading strategy."""
    
    # ATM Straddle Strategy
    strategy = {
        "underlying": 0,  # No direct underlying exposure
        "legs": {
            "call_leg": {
                "type": "call",
                "contracts": 1,  # Buy 1 call
                "selector": [
                    (OPTION_SELECTOR.STRIKE.ATM_REFERENCE_JUMP, 0),  # ATM strike
                    (OPTION_SELECTOR.EXPIRY.NEAREST, 30)  # ~30 days to expiration
                ]
            },
            "put_leg": {
                "type": "put", 
                "contracts": 1,  # Buy 1 put
                "selector": [
                    (OPTION_SELECTOR.STRIKE.ATM_REFERENCE_JUMP, 0),  # ATM strike
                    (OPTION_SELECTOR.EXPIRY.NEAREST, 30)  # ~30 days to expiration
                ]
            }
        }
    }
    
    return strategy

async def run_spx_optalpha_example():
    """Run OptAlpha example with SPX options data."""
    
    print("🚀 SPX OptAlpha Strategy Backtesting")
    print("=" * 50)
    
    # Load SPX options data
    spx_data = load_spx_options_data()
    
    if spx_data is None:
        print("❌ No SPX options data available")
        return False
    
    # Define trading period (use recent data for faster processing)
    end_date = pd.to_datetime(spx_data['date'].max())
    start_date = end_date - timedelta(days=60)  # 2 months of data
    
    trade_range = (start_date, end_date)
    
    logger.info(f"📅 Trading period: {start_date.date()} to {end_date.date()}")
    
    # Create strategy
    strategy = create_sample_strategy()
    
    # Create rebalancing triggers
    rebalance_triggers = [
        (REBALANCER.ON_DAYS_HELD, 7)  # Rebalance weekly
    ]
    
    # Initialize SPX OptAlpha
    logger.info("🔧 Initializing SPX OptAlpha...")
    
    spx_alpha = SPXOptAlpha(
        spx_data=spx_data,
        trade_range=trade_range,
        dfs={},  # We'll use our own data loading
        strategy=strategy,
        rebalance_triggers=rebalance_triggers,
        portfolio_vol=0.15
    )
    
    # Run simulation
    logger.info("🏃 Running OptAlpha simulation...")
    
    try:
        portfolio_df = await spx_alpha.run_simulation()
        
        # Get performance statistics
        perf_stats = spx_alpha.get_perf_stats(plot=True)
        
        print(f"\n🎉 SIMULATION COMPLETED SUCCESSFULLY!")
        print("=" * 40)
        print(f"📊 Portfolio Performance:")
        print(f"   Total Return: {perf_stats.get('total_return', 'N/A')}")
        print(f"   Sharpe Ratio: {perf_stats.get('sharpe_ratio', 'N/A')}")
        print(f"   Max Drawdown: {perf_stats.get('max_drawdown', 'N/A')}")
        print(f"   Win Rate: {perf_stats.get('win_rate', 'N/A')}")
        
        # Save results
        results_file = f"spx_optalpha_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        portfolio_df.to_csv(results_file)
        logger.info(f"💾 Results saved to: {results_file}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Simulation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    
    import asyncio
    
    print("🎯 SPX OptAlpha with 5+ Million Options Records")
    print("=" * 55)
    
    # Run the example
    success = asyncio.run(run_spx_optalpha_example())
    
    if success:
        print("\n✅ SPX OptAlpha example completed successfully!")
        print("📈 Check the generated CSV file for detailed results")
        print("📊 Performance charts should be displayed")
    else:
        print("\n❌ SPX OptAlpha example failed")
        print("💡 Check the logs for error details")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
